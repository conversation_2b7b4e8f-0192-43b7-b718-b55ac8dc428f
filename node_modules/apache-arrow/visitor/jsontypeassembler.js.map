{"version": 3, "sources": ["visitor/jsontypeassembler.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAGrB,8CAAwC;AACxC,2CAAkD;AAClD,wCAAoF;AAOpF,cAAc;AACd,MAAa,iBAAkB,SAAQ,oBAAO;IACnC,KAAK,CAA0B,IAAO;QACzC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IACM,SAAS,CAAsB,EAAE,MAAM,EAAK;QAC/C,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,QAAQ,CAAqB,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAK;QACjE,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;IACnG,CAAC;IACM,UAAU,CAAuB,EAAE,MAAM,EAAE,SAAS,EAAK;QAC5D,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,mBAAS,CAAC,SAAS,CAAC,EAAE,CAAC;IAC1F,CAAC;IACM,WAAW,CAAwB,EAAE,MAAM,EAAK;QACnD,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,gBAAgB,CAA6B,EAAE,MAAM,EAAK;QAC7D,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,SAAS,CAAsB,EAAE,MAAM,EAAK;QAC/C,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,SAAS,CAAsB,EAAE,MAAM,EAAK;QAC/C,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,cAAc,CAA2B,EAAE,MAAM,EAAK;QACzD,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,YAAY,CAAyB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAK;QACjF,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;IACrH,CAAC;IACM,SAAS,CAAuB,EAAE,MAAM,EAAE,IAAI,EAAK;QACtD,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,kBAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;IAC/E,CAAC;IACM,SAAS,CAAsB,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAK;QAC/D,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,kBAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;IACzF,CAAC;IACM,cAAc,CAA2B,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAK;QACzE,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,kBAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;IACzF,CAAC;IACM,aAAa,CAA0B,EAAE,MAAM,EAAE,IAAI,EAAK;QAC7D,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,sBAAY,CAAC,IAAI,CAAC,EAAE,CAAC;IACnF,CAAC;IACM,aAAa,CAA0B,EAAE,MAAM,EAAE,IAAI,EAAK;QAC7D,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,kBAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;IACrF,CAAC;IACM,SAAS,CAAsB,EAAE,MAAM,EAAK;QAC/C,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,WAAW,CAAwB,EAAE,MAAM,EAAK;QACnD,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;IACvD,CAAC;IACM,UAAU,CAAuB,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAK;QAChE,OAAO;YACH,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE;YACvC,MAAM,EAAE,mBAAS,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;YACrC,SAAS,EAAE,CAAC,GAAG,OAAO,CAAC;SAC1B,CAAC;IACN,CAAC;IACM,eAAe,CAA4B,IAAO;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IACM,oBAAoB,CAAiC,EAAE,MAAM,EAAE,SAAS,EAAK;QAChF,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;IAC/E,CAAC;IACM,kBAAkB,CAA+B,EAAE,MAAM,EAAE,QAAQ,EAAK;QAC3E,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;IAC7E,CAAC;IACM,QAAQ,CAAsB,EAAE,MAAM,EAAE,UAAU,EAAK;QAC1D,OAAO,EAAE,MAAM,EAAE,cAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;IACjF,CAAC;CACJ;AAvED,8CAuEC", "file": "jsontypeassembler.js", "sourceRoot": "../src"}