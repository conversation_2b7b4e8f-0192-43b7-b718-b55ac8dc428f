{"version": 3, "sources": ["visitor/typeassembler.ts"], "names": [], "mappings": "AAiBA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAC3C,OAAO,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AAErC,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAwBxC,cAAc;AACd,MAAM,WAAW,aAAc,SAAQ,OAAO;IAC1C,KAAK,CAAC,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM,GAAG,SAAS,CAAC;CACjF;AAED,cAAc;AACd,qBAAa,aAAc,SAAQ,OAAO;IAI/B,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAInD,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAMhD,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAKpD,WAAW,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAIvD,gBAAgB,CAAC,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAIjE,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAInD,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAInD,cAAc,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAI7D,YAAY,CAAC,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAOxD,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAKnD,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAMlD,cAAc,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAS5D,aAAa,CAAC,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAK1D,aAAa,CAAC,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAK1D,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAInD,WAAW,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAIvD,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAQpD,eAAe,CAAC,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAU9D,oBAAoB,CAAC,CAAC,SAAS,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAKxE,kBAAkB,CAAC,CAAC,SAAS,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAKpE,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;CAK3D;AAED,cAAc;AACd,eAAO,MAAM,QAAQ,eAAsB,CAAC", "file": "typeassembler.d.ts", "sourceRoot": "../src"}