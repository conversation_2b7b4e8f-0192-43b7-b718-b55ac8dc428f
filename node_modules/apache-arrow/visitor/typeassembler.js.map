{"version": 3, "sources": ["visitor/typeassembler.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAMrB,8CAAwC;AAExC,2CAAqC;AACrC,yCAAmC;AACnC,+DAAwD;AACxD,+CAAyC;AACzC,2DAAoD;AACpD,2CAAqC;AACrC,2CAAqC;AACrC,uDAAgD;AAChD,iDAA2C;AAC3C,2CAAqC;AACrC,2CAAqC;AACrC,qDAA+C;AAC/C,mDAA6C;AAC7C,mDAA6C;AAC7C,2CAAqC;AACrC,iDAAqD;AACrD,6CAAuC;AACvC,yEAAkE;AAClE,qEAA6D;AAC7D,iEAAyD;AACzD,yCAA2C;AAO3C,cAAc;AACd,MAAa,aAAc,SAAQ,oBAAO;IAC/B,KAAK,CAA0B,IAAO,EAAE,OAAgB;QAC3D,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,cAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,QAAQ,CAAqB,IAAO,EAAE,CAAU;QACnD,YAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChB,YAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,YAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,OAAO,YAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACM,UAAU,CAAuB,IAAO,EAAE,CAAU;QACvD,iCAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACpC,iCAAa,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,OAAO,iCAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IACM,WAAW,CAAwB,KAAQ,EAAE,CAAU;QAC1D,kBAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtB,OAAO,kBAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IACM,gBAAgB,CAA6B,KAAQ,EAAE,CAAU;QACpE,6BAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,6BAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,cAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,cAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,cAAc,CAA2B,KAAQ,EAAE,CAAU;QAChE,yBAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAO,yBAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACM,YAAY,CAAyB,IAAO,EAAE,CAAU;QAC3D,oBAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxB,oBAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,oBAAO,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,oBAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,oBAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACM,SAAS,CAAuB,IAAO,EAAE,CAAU;QACtD,cAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,cAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,SAAS,CAAsB,IAAO,EAAE,CAAU;QACrD,cAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,cAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,cAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,cAAc,CAA2B,IAAO,EAAE,CAAU;QAC/D,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAC;QAC/E,wBAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5B,wBAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACzB,wBAAS,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,wBAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACM,aAAa,CAA0B,IAAO,EAAE,CAAU;QAC7D,sBAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,sBAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,sBAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACM,aAAa,CAA0B,IAAO,EAAE,CAAU;QAC7D,sBAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,sBAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,sBAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,cAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,WAAW,CAAwB,KAAQ,EAAE,CAAU;QAC1D,oBAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvB,OAAO,oBAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IACM,UAAU,CAAuB,IAAO,EAAE,CAAU;QACvD,gBAAK,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,gBAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,gBAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpB,gBAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,gBAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,gBAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACM,eAAe,CAA4B,IAAO,EAAE,CAAU;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC9C,2CAAkB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAC9C,2CAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,2CAAkB,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC1B,2CAAkB,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,2CAAkB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IACM,oBAAoB,CAAiC,IAAO,EAAE,CAAU;QAC3E,sCAAe,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxC,sCAAe,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,OAAO,sCAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IACM,kBAAkB,CAA+B,IAAO,EAAE,CAAU;QACvE,kCAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACpC,kCAAa,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,kCAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IACM,QAAQ,CAAsB,IAAO,EAAE,CAAU;QACpD,YAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjB,YAAI,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,OAAO,YAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;CACJ;AArHD,sCAqHC;AAED,cAAc;AACD,QAAA,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC", "file": "typeassembler.js", "sourceRoot": "../src"}