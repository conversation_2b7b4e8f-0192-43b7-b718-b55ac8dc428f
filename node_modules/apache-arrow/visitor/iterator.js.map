{"version": 3, "sources": ["visitor/iterator.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAGrB,8CAAwC;AACxC,wCAA6C;AAE7C,wCAWoB;AACpB,+CAAmD;AA4DnD,cAAc;AACd,MAAa,eAAgB,SAAQ,oBAAO;CAAI;AAAhD,0CAAgD;AAEhD,cAAc;AACd,SAAS,cAAc,CAAqB,MAAiB;IAEzD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;IAExB,mDAAmD;IACnD,IAAI,MAAM,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI;IACjD,2EAA2E;IAC3E,wEAAwE;IACxE,CAAC,kBAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC;QAC9C,CAAC,kBAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC;QAC/C,CAAC,kBAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,mBAAS,CAAC,IAAI,CAAC,CAChE,EAAE,CAAC;QACA,OAAO,IAAI,0BAAe,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,EAAE;YAC1D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAA8B;IAC9B,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,OAAO,IAAI,0BAAe,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,EAAE;QAC1D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;QACpD,MAAM,IAAI,MAAM,CAAC;QACjB,OAAO,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,cAAc;AACd,MAAM,cAAc;IAGhB,YAAoB,MAAiB;QAAjB,WAAM,GAAN,MAAM,CAAW;QAF7B,UAAK,GAAG,CAAC,CAAC;IAEuB,CAAC;IAE1C,IAAI;QACA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,OAAO;gBACH,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;aACvC,CAAC;QACN,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACvC,CAAC;IAED,CAAC,MAAM,CAAC,QAAQ,CAAC;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,QAAQ,GAAG,cAAc,CAAC;AACpD,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,cAAc,CAAC;AACvD,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,cAAc,CAAC;AACvD,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,cAAc,CAAC;AACvD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,cAAc,CAAC;AACxD,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,cAAc,CAAC;AACxD,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,cAAc,CAAC;AACxD,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;AAC1D,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,cAAc,CAAC;AACvD,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAC5D,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAChE,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,cAAc,CAAC;AACxD,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAChE,eAAe,CAAC,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;AAC1D,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAChE,eAAe,CAAC,SAAS,CAAC,yBAAyB,GAAG,cAAc,CAAC;AACrE,eAAe,CAAC,SAAS,CAAC,yBAAyB,GAAG,cAAc,CAAC;AACrE,eAAe,CAAC,SAAS,CAAC,wBAAwB,GAAG,cAAc,CAAC;AACpE,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,eAAe,GAAG,cAAc,CAAC;AAC3D,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAChE,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAChE,eAAe,CAAC,SAAS,CAAC,mBAAmB,GAAG,cAAc,CAAC;AAC/D,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,cAAc,CAAC;AACxD,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,cAAc,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,cAAc,CAAC;AACvD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,cAAc,CAAC;AACtD,eAAe,CAAC,SAAS,CAAC,eAAe,GAAG,cAAc,CAAC;AAC3D,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAC5D,eAAe,CAAC,SAAS,CAAC,eAAe,GAAG,cAAc,CAAC;AAC3D,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,cAAc,CAAC;AACzD,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,cAAc,CAAC;AAChE,eAAe,CAAC,SAAS,CAAC,sBAAsB,GAAG,cAAc,CAAC;AAClE,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,cAAc,CAAC;AACzD,eAAe,CAAC,SAAS,CAAC,mBAAmB,GAAG,cAAc,CAAC;AAC/D,eAAe,CAAC,SAAS,CAAC,wBAAwB,GAAG,cAAc,CAAC;AACpE,eAAe,CAAC,SAAS,CAAC,wBAAwB,GAAG,cAAc,CAAC;AACpE,eAAe,CAAC,SAAS,CAAC,uBAAuB,GAAG,cAAc,CAAC;AACnE,eAAe,CAAC,SAAS,CAAC,kBAAkB,GAAG,cAAc,CAAC;AAC9D,eAAe,CAAC,SAAS,CAAC,QAAQ,GAAG,cAAc,CAAC;AAEpD,cAAc;AACD,QAAA,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC", "file": "iterator.js", "sourceRoot": "../src"}