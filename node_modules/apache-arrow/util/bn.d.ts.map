{"version": 3, "sources": ["util/bn.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,oBAAoB,EAAqB,MAAM,aAAa,CAAC;AACtE,OAAO,EAAE,UAAU,EAAE,qBAAqB,EAAE,MAAM,kBAAkB,CAAC;AACrE,OAAO,EAAE,WAAW,EAAE,sBAAsB,EAAE,MAAM,kBAAkB,CAAC;AAGvE,cAAc;AACd,eAAO,MAAM,mBAAmB,eAA8B,CAAC;AAE/D,cAAc,CAAC,KAAK,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;AACvD,cAAc,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,UAAU,GAAG,UAAU,CAAC;AACnE,cAAc,CAAC,KAAK,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,WAAW,GAAG,iBAAiB,CAAC;AAgD3F,cAAc;AACd,wBAAgB,cAAc,CAAC,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,UAwB9E;AAED,cAAc;AACd,wBAAgB,cAAc,CAAC,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,MAAM,CAgCtE;AAED,cAAc;AACd,wBAAgB,cAAc,CAAC,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,MAAM,CAOtE;AAsBD,cAAc;AACd,qBAAa,EAAE,CAAC,CAAC,SAAS,WAAW;IACjC,kBAAkB;WACJ,GAAG,CAAC,CAAC,SAAS,WAAW,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAiBjF,kBAAkB;WACJ,MAAM,CAAC,CAAC,SAAS,QAAQ,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAG7D,kBAAkB;WACJ,QAAQ,CAAC,CAAC,SAAS,SAAS,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAGhE,kBAAkB;WACJ,OAAO,CAAC,CAAC,SAAS,SAAS,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAGnD,GAAG,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO;CAGzC;AAED,cAAc;AACd,MAAM,WAAW,EAAE,CAAC,CAAC,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,CAAC,CAAC;IAEhE,KAAK,CAAC,SAAS,oBAAoB,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;IAErE,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,UAAU,EAAE,qBAAqB,CAAC,UAAU,CAAC,CAAC;IACvD,QAAQ,CAAC,WAAW,EAAE,sBAAsB,CAAC,WAAW,CAAC,CAAC;IAE1D,CAAC,MAAM,CAAC,WAAW,CAAC,EACpB,WAAW,GACX,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,aAAa,GACb,aAAa,GACb,mBAAmB,CAAC;IAEpB;;OAEG;IACH,QAAQ,IAAI,MAAM,CAAC;IACnB;;;;OAIG;IACH,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAChC;;;OAGG;IACH,MAAM,IAAI,MAAM,CAAC;IACjB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;CAC9D;AAED,cAAc;AACd,UAAU,cAAc,CAAC,CAAC,SAAS,WAAW;IAE1C,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,MAAM,EAAE,WAAW,CAAC;IAC7B,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC;IAEnC,QAAQ,CAAC,aAAa,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC;IACzE,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;IAC1E,KAAK,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC;IAC/F,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;IAChF,MAAM,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IAC1F,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,MAAM,GAAG,SAAS,CAAC;IACtG,SAAS,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC;IAC/F,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC;IAC3F,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;IACvE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;IAC7C,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;IAC3E,GAAG,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;IACtF,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,MAAM,GAAG,MAAM,CAAC;IACpH,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,CAAC;IAC1I,MAAM,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC;IACzH,WAAW,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,MAAM,GAAG,MAAM,CAAC;IACzH,WAAW,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,CAAC;IAC/I,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC;IAC9H,OAAO,IAAI,CAAC,CAAC;IACb,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;IACjE,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC;IAC/D,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC;IAC9F,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;IACvE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC;IACrD,cAAc,IAAI,MAAM,CAAC;IACzB,OAAO,IAAI,gBAAgB,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9C,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;CACtC", "file": "bn.d.ts", "sourceRoot": "../src"}