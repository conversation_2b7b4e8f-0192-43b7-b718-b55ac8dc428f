{"version": 3, "sources": ["util/compat.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AA+BrB,cAAc,CAAC,MAAM,QAAQ,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC;AAClE,cAAc,CAAC,MAAM,SAAS,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC;AACpE,cAAc,CAAC,MAAM,UAAU,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,UAAU,CAAC;AACtE,cAAc;AACd,wDAAwD;AACjD,MAAM,QAAQ,GAAG,CAAC,CAAM,EAAe,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAAjE,QAAA,QAAQ,YAAyD;AAE9E,cAAc;AACP,MAAM,SAAS,GAAG,CAAU,CAAM,EAAuB,EAAE;IAC9D,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEF,cAAc;AACP,MAAM,YAAY,GAAG,CAAU,CAAM,EAAsB,EAAE;IAChE,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAClD,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAEF,cAAc;AACP,MAAM,UAAU,GAAG,CAAU,CAAM,EAAoB,EAAE;IAC5D,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC;AAFW,QAAA,UAAU,cAErB;AAEF,cAAc;AACP,MAAM,eAAe,GAAG,CAAU,CAAM,EAAyB,EAAE;IACtE,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEF,cAAc;AACP,MAAM,WAAW,GAAG,CAAC,CAAM,EAAsB,EAAE;IACtD,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,IAAA,gBAAQ,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC;AAFW,QAAA,WAAW,eAEtB;AAEF,cAAc;AACP,MAAM,WAAW,GAAG,CAAU,CAAM,EAAqB,EAAE;IAC9D,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC;AAFW,QAAA,WAAW,eAEtB;AAEF,cAAc;AACP,MAAM,gBAAgB,GAAG,CAAU,CAAM,EAA0B,EAAE;IACxE,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC;AAFW,QAAA,gBAAgB,oBAE3B;AAEF,cAAc;AACP,MAAM,gBAAgB,GAAG,CAAU,CAAM,EAA0B,EAAE;IACxE,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AANW,QAAA,gBAAgB,oBAM3B;AAEF,cAAc;AACP,MAAM,YAAY,GAAG,CAAC,CAAM,EAAmB,EAAE;IACpD,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC;AAFW,QAAA,YAAY,gBAEvB;AAEF,cAAc;AACP,MAAM,cAAc,GAAG,CAAC,CAAM,EAAqB,EAAE;IACxD,OAAO,IAAA,4BAAoB,EAAC,CAAC,CAAC,IAAI,QAAQ,CAAO,CAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEF,cAAc;AACP,MAAM,eAAe,GAAG,CAAC,CAAM,EAAiB,EAAE;IACrD,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC,IAAI,IAAA,2BAAmB,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEF,MAAM,iBAAiB,GAAG,CAAU,CAAM,EAA2B,EAAE,CAAC,CAAC,eAAe,IAAI,CAAC,IAAI,gBAAgB,IAAI,CAAC,CAAC,CAAC;AAExH,cAAc;AACP,MAAM,mBAAmB,GAAG,CAAU,CAAM,EAA0B,EAAE;IAC3E,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AALW,QAAA,mBAAmB,uBAK9B;AAEF,cAAc;AACP,MAAM,mBAAmB,GAAG,CAAU,CAAM,EAA0B,EAAE;IAC3E,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACvB,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AALW,QAAA,mBAAmB,uBAK9B;AAEF,cAAc;AACP,MAAM,oBAAoB,GAAG,CAAC,CAAM,EAA8B,EAAE;IACvE,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACpB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AANW,QAAA,oBAAoB,wBAM/B;AAEF,cAAc;AACP,MAAM,oBAAoB,GAAG,CAAC,CAAM,EAA8B,EAAE;IACvE,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACrB,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACrB,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AANW,QAAA,oBAAoB,wBAM/B;AAEF,cAAc;AACP,MAAM,uBAAuB,GAAG,CAAC,CAAM,EAAmB,EAAE;IAC/D,OAAO,IAAA,gBAAQ,EAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACzB,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QAC5B,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACzB,UAAU,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;QACpC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC;AATW,QAAA,uBAAuB,2BASlC", "file": "compat.js", "sourceRoot": "../src"}