{"version": 3, "sources": ["util/bn.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAErB,2CAAsE;AAGtE,2CAA6C;AAE7C,cAAc;AACD,QAAA,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AAM/D,cAAc;AACd,SAAS,MAAM,CAAY,CAAM,EAAE,GAAG,EAAO;IACzC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClB,OAAO,MAAM,CAAC,cAAc,CAAC,IAAA,6BAAiB,EAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACvG,CAAC;IACD,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC/F,CAAC;AAED,MAAM,CAAC,SAAS,CAAC,2BAAmB,CAAC,GAAG,IAAI,CAAC;AAC7C,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,cAAgD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChH,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,UAA8C,KAAc,IAAI,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjI,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,cAAgD,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3G,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,UAA8C,OAAwC,SAAS;IAClI,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,QAAQ,CAAC,CAAC,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,QAAQ,CAAC,CAAC,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,SAAS,CAAC,CAAC,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IACD,aAAa;IACb,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC;AAQF,cAAc;AACd,SAAS,YAAY,CAAY,GAAG,IAA+B,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACzG,cAAc;AACd,SAAS,cAAc,CAAY,GAAG,IAA+B,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3G,cAAc;AACd,SAAS,aAAa,CAAY,GAAG,IAA+B,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAE1G,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;AACnF,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;AACtF,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;AACrF,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,CAAC,CAAC;AACjK,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC,CAAC;AACxK,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC,CAAC;AAErK,0BAA0B;AAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,8BAA8B;AAC7F,MAAM,qBAAqB,GAAG,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,0CAA0C;AAEnG,cAAc;AACd,SAAgB,cAAc,CAA4B,EAAK,EAAE,KAAc;IAC3E,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IAChE,MAAM,KAAK,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;IACrE,MAAM,QAAQ,GAAG,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACrE,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,QAAQ,EAAE,CAAC;QACX,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/E,CAAC;QACD,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;SAAM,CAAC;QACJ,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,IAAI,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,MAAM,GAAG,WAAW,CAAC;QACtC,MAAM,SAAS,GAAG,MAAM,GAAG,WAAW,CAAC;QACvC,OAAO,IAAA,0BAAc,EAAC,QAAQ,CAAC,GAAG,CAAC,IAAA,0BAAc,EAAC,SAAS,CAAC,GAAG,IAAA,0BAAc,EAAC,WAAW,CAAC,CAAC,CAAC;IAChG,CAAC;IACD,OAAO,IAAA,0BAAc,EAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AAxBD,wCAwBC;AAED,cAAc;AACd,SAAgB,cAAc,CAA4B,CAAI;IAC1D,mCAAmC;IACnC,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACpE,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/B,CAAC;IAED,mBAAmB;IACnB,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;QACf,OAAO,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,KAAK,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAEtE,0BAA0B;IAC1B,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;QACrB,OAAO,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IAED,0BAA0B;IAC1B,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;IACtB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,MAAM,OAAO,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC;QAC9B,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QACnB,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,OAAO,GAAG,sBAAsB,CAAM,KAAK,CAAC,CAAC;IACnD,OAAO,IAAI,OAAO,EAAE,CAAC;AACzB,CAAC;AAhCD,wCAgCC;AAED,cAAc;AACd,SAAgB,cAAc,CAA4B,CAAI;IAC1D,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACpE,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;SAAM,CAAC;QACJ,OAAY,cAAc,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;AACL,CAAC;AAPD,wCAOC;AAED,cAAc;AACd,SAAS,sBAAsB,CAA4B,CAAI;IAC3D,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACvE,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;IACpF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,GAAG,CAAC;QACA,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YACrC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACzC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACvC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACvC,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC;IACrC,CAAC,QAAQ,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;IAC3D,OAAO,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,GAAG,CAAC;AACzB,CAAC;AAED,cAAc;AACd,MAAa,EAAE;IACX,kBAAkB;IACX,MAAM,CAAC,GAAG,CAAwB,GAAM,EAAE,QAAkB;QAC/D,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,IAAI,CAAC,CAAC,OAAO,IAAU,YAAa,CAAC,GAAG,CAAgB,CAAC;YAC9D,KAAK,KAAK,CAAC,CAAC,OAAO,IAAU,cAAe,CAAC,GAAG,CAAgB,CAAC;QACrE,CAAC;QACD,QAAQ,GAAG,CAAC,WAAW,EAAE,CAAC;YACtB,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,UAAU,CAAC;YAChB,KAAK,aAAa;gBACd,OAAO,IAAU,YAAa,CAAC,GAAG,CAAgB,CAAC;QAC3D,CAAC;QACD,IAAI,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,CAAC;YACxB,OAAO,IAAU,aAAc,CAAC,GAAG,CAAgB,CAAC;QACxD,CAAC;QACD,OAAO,IAAU,cAAe,CAAC,GAAG,CAAgB,CAAC;IACzD,CAAC;IACD,kBAAkB;IACX,MAAM,CAAC,MAAM,CAAqB,GAAM;QAC3C,OAAO,IAAU,YAAa,CAAC,GAAG,CAAgB,CAAC;IACvD,CAAC;IACD,kBAAkB;IACX,MAAM,CAAC,QAAQ,CAAsB,GAAM;QAC9C,OAAO,IAAU,cAAe,CAAC,GAAG,CAAgB,CAAC;IACzD,CAAC;IACD,kBAAkB;IACX,MAAM,CAAC,OAAO,CAAsB,GAAM;QAC7C,OAAO,IAAU,aAAc,CAAC,GAAG,CAAgB,CAAC;IACxD,CAAC;IACD,YAAY,GAAM,EAAE,QAAkB;QAClC,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAQ,CAAC;IACxC,CAAC;CACJ;AAlCD,gBAkCC", "file": "bn.js", "sourceRoot": "../src"}