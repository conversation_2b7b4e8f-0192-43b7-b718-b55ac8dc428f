{"version": 3, "sources": ["util/compat.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AA+BrB,cAAc,CAAC,MAAM,QAAQ,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC;AAClE,cAAc,CAAC,MAAM,SAAS,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC;AACpE,cAAc,CAAC,MAAM,UAAU,GAAG,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,UAAU,CAAC;AACtE,cAAc;AACd,wDAAwD;AACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,CAAM,EAAe,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAE9E,cAAc;AACd,MAAM,CAAC,MAAM,SAAS,GAAG,CAAU,CAAM,EAAuB,EAAE;IAC9D,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,YAAY,GAAG,CAAU,CAAM,EAAsB,EAAE;IAChE,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,UAAU,GAAG,CAAU,CAAM,EAAoB,EAAE;IAC5D,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,eAAe,GAAG,CAAU,CAAM,EAAyB,EAAE;IACtE,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,CAAM,EAAsB,EAAE;IACtD,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,WAAW,GAAG,CAAU,CAAM,EAAqB,EAAE;IAC9D,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAU,CAAM,EAA0B,EAAE;IACxE,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAU,CAAM,EAA0B,EAAE;IACxE,OAAO,QAAQ,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,CAAM,EAAmB,EAAE;IACpD,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,CAAM,EAAqB,EAAE;IACxD,OAAO,oBAAoB,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAO,CAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,CAAM,EAAiB,EAAE;IACrD,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAU,CAAM,EAA2B,EAAE,CAAC,CAAC,eAAe,IAAI,CAAC,IAAI,gBAAgB,IAAI,CAAC,CAAC,CAAC;AAExH,cAAc;AACd,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAU,CAAM,EAA0B,EAAE;IAC3E,OAAO,QAAQ,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAU,CAAM,EAA0B,EAAE;IAC3E,OAAO,QAAQ,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACvB,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAM,EAA8B,EAAE;IACvE,OAAO,QAAQ,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACpB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAM,EAA8B,EAAE;IACvE,OAAO,QAAQ,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACrB,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACrB,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACxB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,cAAc;AACd,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,CAAM,EAAmB,EAAE;IAC/D,OAAO,QAAQ,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACtB,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACzB,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;QAC5B,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACzB,UAAU,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;QACpC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC", "file": "compat.mjs", "sourceRoot": "../src"}