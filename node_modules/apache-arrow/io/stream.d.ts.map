{"version": 3, "sources": ["io/stream.ts"], "names": [], "mappings": ";AAmBA,OAAO,EAAiB,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAChF,OAAO,EAAiC,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AAQxF,cAAc;AACd,MAAM,MAAM,YAAY,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;AAC7F,cAAc;AACd,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;AAEnI,cAAc;AACd,qBAAa,cAAc,CAAC,CAAC,SAAS,oBAAoB,GAAG,UAAU,CAAE,SAAQ,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;IAC/F,KAAK,CAAC,KAAK,EAAE,oBAAoB,GAAG,UAAU;IAK9C,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM;IAC5B,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;IAMvC,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU;IACpC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;CAYzD;AAED,cAAc;AACd,qBAAa,UAAW,YAAW,gBAAgB,CAAC,UAAU,CAAC;IAC3D,QAAgB,MAAM,CAA+B;gBACzC,MAAM,CAAC,EAAE,QAAQ,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;IAK1E,CAAC,MAAM,CAAC,QAAQ,CAAC;IACV,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;IAChB,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;IACjB,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG;IAClB,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IACzB,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;CACnC;AAED,cAAc;AACd,qBAAa,eAAgB,YAAW,QAAQ,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC,UAAU,CAAC;IAC3F,QAAgB,MAAM,CAAoC;gBAC9C,MAAM,CAAC,EAAE,WAAW,CAAC,oBAAoB,CAAC,GAAG,QAAQ,GAAG,cAAc,CAAC,oBAAoB,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,GAAG,QAAQ,CAAC,oBAAoB,CAAC;IAmBvM,CAAC,MAAM,CAAC,aAAa,CAAC;IACf,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;IAChB,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;IACjB,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG;IACzB,IAAW,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAA+B;IAC1D,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG;IACnB,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IACzB,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;CACnC", "file": "stream.d.ts", "sourceRoot": "../src"}