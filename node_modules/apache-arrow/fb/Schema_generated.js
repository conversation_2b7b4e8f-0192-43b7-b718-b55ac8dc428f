"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.Utf8 = exports.UnionMode = exports.Union = exports.unionListToType = exports.unionToType = exports.Type = exports.Timestamp = exports.TimeUnit = exports.Time = exports.Struct_ = exports.Schema = exports.RunEndEncoded = exports.Precision = exports.Null = exports.Map = exports.List = exports.LargeUtf8 = exports.LargeList = exports.LargeBinary = exports.KeyValue = exports.IntervalUnit = exports.Interval = exports.Int = exports.FloatingPoint = exports.FixedSizeList = exports.FixedSizeBinary = exports.Field = exports.Endianness = exports.Duration = exports.DictionaryKind = exports.DictionaryEncoding = exports.Decimal = exports.DateUnit = exports.Date = exports.Bool = exports.Binary = void 0;
var binary_js_1 = require("./binary.js");
Object.defineProperty(exports, "Binary", { enumerable: true, get: function () { return binary_js_1.Binary; } });
var bool_js_1 = require("./bool.js");
Object.defineProperty(exports, "Bool", { enumerable: true, get: function () { return bool_js_1.Bool; } });
var date_js_1 = require("./date.js");
Object.defineProperty(exports, "Date", { enumerable: true, get: function () { return date_js_1.Date; } });
var date_unit_js_1 = require("./date-unit.js");
Object.defineProperty(exports, "DateUnit", { enumerable: true, get: function () { return date_unit_js_1.DateUnit; } });
var decimal_js_1 = require("./decimal.js");
Object.defineProperty(exports, "Decimal", { enumerable: true, get: function () { return decimal_js_1.Decimal; } });
var dictionary_encoding_js_1 = require("./dictionary-encoding.js");
Object.defineProperty(exports, "DictionaryEncoding", { enumerable: true, get: function () { return dictionary_encoding_js_1.DictionaryEncoding; } });
var dictionary_kind_js_1 = require("./dictionary-kind.js");
Object.defineProperty(exports, "DictionaryKind", { enumerable: true, get: function () { return dictionary_kind_js_1.DictionaryKind; } });
var duration_js_1 = require("./duration.js");
Object.defineProperty(exports, "Duration", { enumerable: true, get: function () { return duration_js_1.Duration; } });
var endianness_js_1 = require("./endianness.js");
Object.defineProperty(exports, "Endianness", { enumerable: true, get: function () { return endianness_js_1.Endianness; } });
var field_js_1 = require("./field.js");
Object.defineProperty(exports, "Field", { enumerable: true, get: function () { return field_js_1.Field; } });
var fixed_size_binary_js_1 = require("./fixed-size-binary.js");
Object.defineProperty(exports, "FixedSizeBinary", { enumerable: true, get: function () { return fixed_size_binary_js_1.FixedSizeBinary; } });
var fixed_size_list_js_1 = require("./fixed-size-list.js");
Object.defineProperty(exports, "FixedSizeList", { enumerable: true, get: function () { return fixed_size_list_js_1.FixedSizeList; } });
var floating_point_js_1 = require("./floating-point.js");
Object.defineProperty(exports, "FloatingPoint", { enumerable: true, get: function () { return floating_point_js_1.FloatingPoint; } });
var int_js_1 = require("./int.js");
Object.defineProperty(exports, "Int", { enumerable: true, get: function () { return int_js_1.Int; } });
var interval_js_1 = require("./interval.js");
Object.defineProperty(exports, "Interval", { enumerable: true, get: function () { return interval_js_1.Interval; } });
var interval_unit_js_1 = require("./interval-unit.js");
Object.defineProperty(exports, "IntervalUnit", { enumerable: true, get: function () { return interval_unit_js_1.IntervalUnit; } });
var key_value_js_1 = require("./key-value.js");
Object.defineProperty(exports, "KeyValue", { enumerable: true, get: function () { return key_value_js_1.KeyValue; } });
var large_binary_js_1 = require("./large-binary.js");
Object.defineProperty(exports, "LargeBinary", { enumerable: true, get: function () { return large_binary_js_1.LargeBinary; } });
var large_list_js_1 = require("./large-list.js");
Object.defineProperty(exports, "LargeList", { enumerable: true, get: function () { return large_list_js_1.LargeList; } });
var large_utf8_js_1 = require("./large-utf8.js");
Object.defineProperty(exports, "LargeUtf8", { enumerable: true, get: function () { return large_utf8_js_1.LargeUtf8; } });
var list_js_1 = require("./list.js");
Object.defineProperty(exports, "List", { enumerable: true, get: function () { return list_js_1.List; } });
var map_js_1 = require("./map.js");
Object.defineProperty(exports, "Map", { enumerable: true, get: function () { return map_js_1.Map; } });
var null_js_1 = require("./null.js");
Object.defineProperty(exports, "Null", { enumerable: true, get: function () { return null_js_1.Null; } });
var precision_js_1 = require("./precision.js");
Object.defineProperty(exports, "Precision", { enumerable: true, get: function () { return precision_js_1.Precision; } });
var run_end_encoded_js_1 = require("./run-end-encoded.js");
Object.defineProperty(exports, "RunEndEncoded", { enumerable: true, get: function () { return run_end_encoded_js_1.RunEndEncoded; } });
var schema_js_1 = require("./schema.js");
Object.defineProperty(exports, "Schema", { enumerable: true, get: function () { return schema_js_1.Schema; } });
var struct__js_1 = require("./struct-.js");
Object.defineProperty(exports, "Struct_", { enumerable: true, get: function () { return struct__js_1.Struct_; } });
var time_js_1 = require("./time.js");
Object.defineProperty(exports, "Time", { enumerable: true, get: function () { return time_js_1.Time; } });
var time_unit_js_1 = require("./time-unit.js");
Object.defineProperty(exports, "TimeUnit", { enumerable: true, get: function () { return time_unit_js_1.TimeUnit; } });
var timestamp_js_1 = require("./timestamp.js");
Object.defineProperty(exports, "Timestamp", { enumerable: true, get: function () { return timestamp_js_1.Timestamp; } });
var type_js_1 = require("./type.js");
Object.defineProperty(exports, "Type", { enumerable: true, get: function () { return type_js_1.Type; } });
Object.defineProperty(exports, "unionToType", { enumerable: true, get: function () { return type_js_1.unionToType; } });
Object.defineProperty(exports, "unionListToType", { enumerable: true, get: function () { return type_js_1.unionListToType; } });
var union_js_1 = require("./union.js");
Object.defineProperty(exports, "Union", { enumerable: true, get: function () { return union_js_1.Union; } });
var union_mode_js_1 = require("./union-mode.js");
Object.defineProperty(exports, "UnionMode", { enumerable: true, get: function () { return union_mode_js_1.UnionMode; } });
var utf8_js_1 = require("./utf8.js");
Object.defineProperty(exports, "Utf8", { enumerable: true, get: function () { return utf8_js_1.Utf8; } });

//# sourceMappingURL=Schema_generated.js.map
