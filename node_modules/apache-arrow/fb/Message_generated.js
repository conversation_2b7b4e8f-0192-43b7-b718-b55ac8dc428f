"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tensor = exports.SparseTensor = exports.Schema = exports.RecordBatch = exports.MetadataVersion = exports.unionListToMessageHeader = exports.unionToMessageHeader = exports.MessageHeader = exports.Message = exports.KeyValue = exports.FieldNode = exports.DictionaryBatch = exports.CompressionType = exports.Buffer = exports.BodyCompressionMethod = exports.BodyCompression = void 0;
var body_compression_js_1 = require("./body-compression.js");
Object.defineProperty(exports, "BodyCompression", { enumerable: true, get: function () { return body_compression_js_1.BodyCompression; } });
var body_compression_method_js_1 = require("./body-compression-method.js");
Object.defineProperty(exports, "BodyCompressionMethod", { enumerable: true, get: function () { return body_compression_method_js_1.BodyCompressionMethod; } });
var buffer_js_1 = require("./buffer.js");
Object.defineProperty(exports, "Buffer", { enumerable: true, get: function () { return buffer_js_1.Buffer; } });
var compression_type_js_1 = require("./compression-type.js");
Object.defineProperty(exports, "CompressionType", { enumerable: true, get: function () { return compression_type_js_1.CompressionType; } });
var dictionary_batch_js_1 = require("./dictionary-batch.js");
Object.defineProperty(exports, "DictionaryBatch", { enumerable: true, get: function () { return dictionary_batch_js_1.DictionaryBatch; } });
var field_node_js_1 = require("./field-node.js");
Object.defineProperty(exports, "FieldNode", { enumerable: true, get: function () { return field_node_js_1.FieldNode; } });
var key_value_js_1 = require("./key-value.js");
Object.defineProperty(exports, "KeyValue", { enumerable: true, get: function () { return key_value_js_1.KeyValue; } });
var message_js_1 = require("./message.js");
Object.defineProperty(exports, "Message", { enumerable: true, get: function () { return message_js_1.Message; } });
var message_header_js_1 = require("./message-header.js");
Object.defineProperty(exports, "MessageHeader", { enumerable: true, get: function () { return message_header_js_1.MessageHeader; } });
Object.defineProperty(exports, "unionToMessageHeader", { enumerable: true, get: function () { return message_header_js_1.unionToMessageHeader; } });
Object.defineProperty(exports, "unionListToMessageHeader", { enumerable: true, get: function () { return message_header_js_1.unionListToMessageHeader; } });
var metadata_version_js_1 = require("./metadata-version.js");
Object.defineProperty(exports, "MetadataVersion", { enumerable: true, get: function () { return metadata_version_js_1.MetadataVersion; } });
var record_batch_js_1 = require("./record-batch.js");
Object.defineProperty(exports, "RecordBatch", { enumerable: true, get: function () { return record_batch_js_1.RecordBatch; } });
var schema_js_1 = require("./schema.js");
Object.defineProperty(exports, "Schema", { enumerable: true, get: function () { return schema_js_1.Schema; } });
var sparse_tensor_js_1 = require("./sparse-tensor.js");
Object.defineProperty(exports, "SparseTensor", { enumerable: true, get: function () { return sparse_tensor_js_1.SparseTensor; } });
var tensor_js_1 = require("./tensor.js");
Object.defineProperty(exports, "Tensor", { enumerable: true, get: function () { return tensor_js_1.Tensor; } });

//# sourceMappingURL=Message_generated.js.map
