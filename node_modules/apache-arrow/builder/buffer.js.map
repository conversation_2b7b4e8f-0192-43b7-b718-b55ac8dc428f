{"version": 3, "sources": ["builder/buffer.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAErB,iDAA2C;AAI3C,cAAc;AACd,SAAS,6BAA6B,CAAC,GAAW,EAAE,GAAW;IAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAC7C,OAAO,CAAC,CAAC,WAAW,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;AAC/D,CAAC;AAED,cAAc;AACd,SAAS,WAAW,CAAqC,GAAM,EAAE,GAAG,GAAG,CAAC;IACpE,OAAO,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;QACtB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAM,CAAC,CAAC;QAC3B,IAAA,kBAAM,EAAC,IAAK,GAAG,CAAC,WAAmB,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,cAAc;AACd,MAAa,aAAa;IAEtB,YAAY,UAAwB,EAAE,WAAW,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;QAC7D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAM,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAC;QACtD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;IAChC,CAAC;IAQD,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;IACzE,CAAC;IACD,IAAW,cAAc,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACxE,IAAW,kBAAkB,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IAElE,aAAa;IACN,GAAG,CAAC,KAAa,EAAE,KAAW,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC;IAChD,MAAM,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5D,OAAO,CAAC,KAAa;QACxB,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YACpC,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC;oBACvB,CAAC,CAAC,6BAA6B,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC;oBACnE,CAAC,CAAC,6BAA6B,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CACtE,CAAC;YACN,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QAC7B,MAAM,GAAG,6BAA6B,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACrF,MAAM,KAAK,GAAG,WAAW,CAAI,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACjB,CAAC;IACM,KAAK;QACR,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAO,CAAC;QACxC,OAAO,IAAI,CAAC;IAChB,CAAC;IACS,OAAO,CAAC,SAAiB;QAC/B,OAAO,IAAI,CAAC,MAAM,GAAG,WAAW,CAAI,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;CACJ;AAtDD,sCAsDC;AAED,cAAc;AACd,MAAa,iBAAsD,SAAQ,aAAgB;IAChF,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,GAAG,CAAC,KAAa,IAAU,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,GAAG,CAAC,KAAa,EAAE,KAAW;QACjC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AARD,8CAQC;AAED,cAAc;AACd,MAAa,mBAAoB,SAAQ,iBAA6B;IAElE;QAAgB,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAErC,aAAQ,GAAG,CAAC,CAAC;IAFyB,CAAC;IAG9C,IAAW,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxD,GAAG,CAAC,GAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjE,GAAG,CAAC,GAAW,EAAE,GAAW;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;QACpE,sFAAsF;QACtF,sFAAsF;QACtF,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC;YAC9D,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,KAAK;QACR,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACJ;AApBD,kDAoBC;AAED,cAAc;AACd,MAAa,oBAAyC,SAAQ,iBAAoC;IAC9F,YAAY,IAAO;QACf,KAAK,CAAC,IAAI,CAAC,eAA+C,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtE,CAAC;IACM,MAAM,CAAC,KAA2B;QACrC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IACM,GAAG,CAAC,KAAa,EAAE,KAA2B;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;QACvD,IAAI,MAAM,GAAG,KAAK,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QACD,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAC1C,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;QACjC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;CACJ;AAtBD,oDAsBC", "file": "buffer.js", "sourceRoot": "../src"}