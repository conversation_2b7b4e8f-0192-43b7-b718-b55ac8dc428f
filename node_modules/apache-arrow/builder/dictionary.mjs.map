{"version": 3, "sources": ["builder/dictionary.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAIrB,OAAO,EAAE,UAAU,EAAY,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,OAAO,EAAkB,MAAM,eAAe,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAQ9C,cAAc;AACd,MAAM,OAAO,iBAAqD,SAAQ,OAAiB;IAQvF,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAsC;QACnH,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAM,EAAE,CAAC,CAAC;QAC7F,IAAI,CAAC,MAAM,GAAQ,IAAI,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,CAA6B,CAAC;QAC3G,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,CAA6B,CAAC;QAChH,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QAC7B,CAAC;IACL,CAAC;IAED,IAAW,MAAM,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,IAAW,SAAS,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;IACzD,IAAW,UAAU,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;IAC3D,IAAW,UAAU,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IACxF,IAAW,cAAc,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC;IACpG,IAAW,kBAAkB,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC;IACzG,OAAO,CAAC,KAA0B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3E,QAAQ,CAAC,KAAa,EAAE,KAAc;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,OAAO,KAAK,CAAC;IACjB,CAAC;IACM,QAAQ,CAAC,KAAa,EAAE,KAAkB;QAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACpB,aAAa,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QACjG,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IACM,KAAK;QACR,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAClD,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAqC,CAAC;QAC9D,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,MAAM;QACT,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;IAC1B,CAAC;IACM,KAAK;QACR,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IACM,UAAU,CAAC,GAAQ;QACtB,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;IACpD,CAAC;CACJ", "file": "dictionary.mjs", "sourceRoot": "../src"}