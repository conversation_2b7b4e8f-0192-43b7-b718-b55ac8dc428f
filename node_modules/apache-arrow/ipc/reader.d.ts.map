{"version": 3, "sources": ["ipc/reader.ts"], "names": [], "mappings": ";;;AAkBA,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,QAAQ,EAAU,OAAO,EAAE,MAAM,YAAY,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAChD,OAAO,KAAK,QAAQ,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAC9D,OAAO,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,MAAM,eAAe,CAAC;AAExE,OAAO,EAAE,WAAW,EAAwC,MAAM,mBAAmB,CAAC;AACtF,OAAO,EACH,UAAU,EACV,aAAa,EAEb,eAAe,EAClB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,aAAa,EAAE,kBAAkB,EAEpC,MAAM,cAAc,CAAC;AAStB,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAEzD,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,aAAa,CAAC;AACpD,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;AACjE,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC;AAC5F,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AACzG,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC,oBAAoB,CAAC,GAAG,aAAa,CAAC,oBAAoB,CAAC,CAAC;AACpJ,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnG,cAAc,CAAC,MAAM,MAAM,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAEtG,cAAc,CAAC,KAAK,WAAW,GAAG;IAAE,WAAW,CAAC,EAAE,OAAO,CAAA;CAAE,CAAC;AAC5D,cAAc,CAAC,KAAK,kBAAkB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,IAAI,qBAAqB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;AACxH,cAAc,CAAC,KAAK,uBAAuB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,IAAI,0BAA0B,CAAC,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC,CAAC,CAAC;AACvI,cAAc,CAAC,KAAK,sBAAsB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,IAAI,qBAAqB,CAAC,CAAC,CAAC,GAAG,0BAA0B,CAAC,CAAC,CAAC,CAAC;AAC/H,cAAc,CAAC,KAAK,wBAAwB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,IAAI,uBAAuB,CAAC,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC,CAAC,CAAC;AAErI,qBAAa,iBAAiB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAE3F,SAAS,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAC3C,SAAS,aAAa,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC;IAKrD,IAAW,MAAM,YAAgC;IACjD,IAAW,MAAM,cAAgC;IACjD,IAAW,WAAW,YAAqC;IAC3D,IAAW,YAAY,6BAAsC;IAC7D,IAAW,eAAe,WAAyC;IACnE,IAAW,gBAAgB,WAA0C;IACrE,IAAW,MAAM,IAAI,MAAM,GAAG,IAAI,CAA2D;IAEtF,MAAM,IAAI,IAAI,IAAI,kBAAkB,CAAC,CAAC,CAAC;IACvC,OAAO,IAAI,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC;IAC7C,MAAM,IAAI,IAAI,IAAI,sBAAsB,CAAC,CAAC,CAAC;IAC3C,QAAQ,IAAI,IAAI,IAAI,wBAAwB,CAAC,CAAC,CAAC;IAE/C,IAAI;IAGJ,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;IAGjB,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG;IAGlB,MAAM;IAGN,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;IAMtC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW;IAI1B,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAGtF,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAGrD,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAG/D,WAAW,IAAI,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAM7C,YAAY,IAAI,OAAO,QAAQ,EAAE,QAAQ;IAQhD,kBAAkB;WAEJ,WAAW,CAAC,OAAO,CAAC,EAAE,aAAa,GAAG;QAAE,WAAW,EAAE,OAAO,CAAA;KAAE,GAAG,MAAM;IAGrF,kBAAkB;WACJ,UAAU,CAAC,CAAC,SAAS,OAAO,EAEtC,gBAAgB,CAAC,EAAE,yBAAyB,EAE5C,gBAAgB,CAAC,EAAE;QAAE,WAAW,EAAE,OAAO,CAAA;KAAE,GAC5C;QAAE,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QAAC,QAAQ,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;KAAE;WAIvE,IAAI,CAAC,CAAC,SAAS,iBAAiB,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC;WAC/C,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,uBAAuB,CAAC,CAAC,CAAC;WAC3E,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;WACpF,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,qBAAqB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;WACtG,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;WAC/G,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC,CAAC,CAAC;WACzH,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC,CAAC,CAAC;WAiBzH,OAAO,CAAC,CAAC,SAAS,iBAAiB,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,SAAS,kBAAkB,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;WAC9H,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;WAChG,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;WACrG,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;WAC3H,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;WAChI,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;WACrG,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,EAAE,QAAQ,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;CAUtH;AAsBD,cAAc;AACd,qBAAa,uBAAuB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;IAC1E,SAAS,CAAC,KAAK,EAAE,2BAA2B,CAAC,CAAC,CAAC;gBAArC,KAAK,EAAE,2BAA2B,CAAC,CAAC,CAAC;IACpD,OAAO;IACP,CAAC,MAAM,CAAC,QAAQ,CAAC;IACV,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAChF;AACD,cAAc;AACd,qBAAa,4BAA4B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;IAC/E,SAAS,CAAC,KAAK,EAAE,gCAAgC,CAAC,CAAC,CAAC;gBAA1C,KAAK,EAAE,gCAAgC,CAAC,CAAC,CAAC;IACnD,OAAO;IAKb,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,CAAC,aAAa,CAAC;CAChC;AACD,cAAc;AACd,qBAAa,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,uBAAuB,CAAC,CAAC,CAAC;IAC9E,SAAS,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC,CAAC;gBAAnC,KAAK,EAAE,yBAAyB,CAAC,CAAC,CAAC;CAC5D;AACD,cAAc;AACd,qBAAa,0BAA0B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,4BAA4B,CAAC,CAAC,CAAC;IACxF,SAAS,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC,CAAC;gBAAxC,KAAK,EAAE,8BAA8B,CAAC,CAAC,CAAC;CACjE;AAMD,cAAc;AACd,MAAM,WAAW,uBAAuB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;IAC1F,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,GAAG,SAAS,GAAG,IAAI,CAAC;IAC9C,MAAM,IAAI,IAAI,CAAC;IACf,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;CACrD;AAED,cAAc;AACd,MAAM,WAAW,4BAA4B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;IAC/F,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,GAAG,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACvD,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACxB,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9D;AAED,cAAc;AACd,MAAM,WAAW,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,uBAAuB,CAAC,CAAC,CAAC;IAC9F,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CACzD;AAED,cAAc;AACd,MAAM,WAAW,0BAA0B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,4BAA4B,CAAC,CAAC,CAAC;IACxG,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;CAClE;AAED,cAAc;AACd,KAAK,sBAAsB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,IAC/C,yBAAyB,CAAC,CAAC,CAAC,GAC5B,yBAAyB,CAAC,CAAC,CAAC,GAC5B,2BAA2B,CAAC,CAAC,CAAC,GAC9B,8BAA8B,CAAC,CAAC,CAAC,GACjC,gCAAgC,CAAC,CAAC,CAAC,CAAC;AAExC,cAAc;AACd,UAAU,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG;IAEnD,MAAM,EAAE,OAAO,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAClB,WAAW,EAAE,OAAO,CAAC;IACrB,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAElC,MAAM,IAAI,IAAI,IAAI,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAC5C,QAAQ,IAAI,IAAI,IAAI,wBAAwB,CAAC,CAAC,CAAC,CAAC;IAChD,MAAM,IAAI,IAAI,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACxC,OAAO,IAAI,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAE9C,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;CAC1C;AAED,cAAc;AACd,UAAU,2BAA2B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,qBAAqB,CAAC,CAAC,CAAC;IAE3F,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC;IAClC,MAAM,IAAI,IAAI,CAAC;IAEf,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAElD,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;CACzD;AAED,cAAc;AACd,UAAU,gCAAgC,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,qBAAqB,CAAC,CAAC,CAAC;IAEhG,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC3C,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAExB,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3D,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;CACnE;AAED,cAAc;AACd,UAAU,yBAAyB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,2BAA2B,CAAC,CAAC,CAAC;IAC/F,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CACzD;AAED,cAAc;AACd,UAAU,8BAA8B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,gCAAgC,CAAC,CAAC,CAAC;IACzG,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;CAClE;AAED,cAAc;AACd,uBAAe,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,YAAW,qBAAqB,CAAC,CAAC,CAAC;IAE9E,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,UAAS;IACf,WAAW,UAAQ;IACnB,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAEzC,SAAS,CAAC,gBAAgB,SAAK;IAC/B,SAAS,CAAC,iBAAiB,SAAK;IAChC,IAAW,eAAe,WAAoC;IAC9D,IAAW,gBAAgB,WAAqC;gBAEpD,YAAY,2BAA4B;IAiBpD,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,GAAG;IAKlE,SAAS,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG;IAU1E,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,EAAE;CAG9F;AAED,cAAc;AACd,cAAM,2BAA2B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,qBAAqB,CAAC,CAAC,CAAE,YAAW,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnI,SAAS,CAAC,OAAO,EAAE,aAAa,CAAC;IACjC,SAAS,CAAC,OAAO,EAAE,UAAU,GAAG,aAAa,CAAC;gBAElC,MAAM,EAAE,UAAU,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAO3E,MAAM,IAAI,IAAI,IAAI,kBAAkB,CAAC,CAAC,CAAC;IACvC,QAAQ,IAAI,IAAI,IAAI,wBAAwB,CAAC,CAAC,CAAC;IA2DtD,SAAS,CAAC,2BAA2B,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;CAGjF;AAED,cAAc;AACd,cAAM,gCAAgC,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,qBAAqB,CAAC,CAAC,CAAE,YAAW,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAE7I,SAAS,CAAC,OAAO,EAAE,eAAe,CAAC;IACnC,SAAS,CAAC,OAAO,EAAE,kBAAkB,CAAC;gBAE1B,MAAM,EAAE,eAAe,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAIhE,OAAO,IAAI,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC;IAC7C,QAAQ,IAAI,IAAI,IAAI,wBAAwB,CAAC,CAAC,CAAC;cA2DtC,2BAA2B,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;CAGvF;AAED,cAAc;AACd,cAAM,yBAAyB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,2BAA2B,CAAC,CAAC,CAAC;IAE3F,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAC3B,UAAkB,OAAO,EAAE,gBAAgB,CAAC;IAC5C,IAAW,MAAM,WAA4B;IAC7C,IAAW,eAAe,WAA8D;IACxF,IAAW,gBAAgB,WAA+D;gBAE9E,MAAM,EAAE,gBAAgB,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAGxF,MAAM,IAAI,IAAI,IAAI,kBAAkB,CAAC,CAAC,CAAC;IACvC,MAAM,IAAI,IAAI,IAAI,sBAAsB,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW;IAwBjC,SAAS,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM;IAY5C,SAAS,CAAC,WAAW;IAOrB,SAAS,CAAC,2BAA2B,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;CAUrG;AAED,cAAc;AACd,cAAM,8BAA8B,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,gCAAgC,CAAC,CAAC,CACpG,YAAW,8BAA8B,CAAC,CAAC,CAAC;IAE5C,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAC3B,UAAkB,OAAO,EAAE,qBAAqB,CAAC;IACjD,IAAW,MAAM,WAA4B;IAC7C,IAAW,eAAe,WAA8D;IACxF,IAAW,gBAAgB,WAA+D;gBAE9E,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC3E,MAAM,EAAE,UAAU,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAMnF,MAAM,IAAI,IAAI,IAAI,sBAAsB,CAAC,CAAC,CAAC;IAC3C,OAAO,IAAI,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC,OAAO,CAAC,EAAE,WAAW;cAwBvB,oBAAoB,CAAC,KAAK,EAAE,MAAM;cAYlC,WAAW;cAQX,2BAA2B,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CAUpH;AAED,cAAc;AACd,cAAM,yBAAyB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,2BAA2B,CAAC,CAAC,CAAC;gBAC/E,MAAM,EAAE,aAAa,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAGrE,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,EAAE;CAG9F", "file": "reader.d.ts", "sourceRoot": "../src"}