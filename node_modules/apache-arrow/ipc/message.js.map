{"version": 3, "sources": ["ipc/message.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAErB,wCAA2C;AAC3C,6CAAyC;AACzC,sDAAgD;AAChD,iDAAiD;AACjD,2CAAsD;AACtD,iDAAuE;AACvE,+CAA8E;AAC9E,uDAA0F;AAE1F,cAAc,CAAC,MAAM,kBAAkB,GAAG,CAAC,IAAmB,EAAE,EAAE,CAAC,YAAY,uBAAa,CAAC,IAAI,CAAC,+CAA+C,CAAC;AAClJ,cAAc,CAAC,MAAM,WAAW,GAAG,CAAC,IAAmB,EAAE,EAAE,CAAC,wCAAwC,uBAAa,CAAC,IAAI,CAAC,+BAA+B,CAAC;AACvJ,cAAc,CAAC,MAAM,sBAAsB,GAAG,CAAC,QAAgB,EAAE,MAAc,EAAE,EAAE,CAAC,oBAAoB,QAAQ,kCAAkC,MAAM,GAAG,CAAC;AAC5J,cAAc,CAAC,MAAM,wBAAwB,GAAG,CAAC,QAAgB,EAAE,MAAc,EAAE,EAAE,CAAC,oBAAoB,QAAQ,0CAA0C,MAAM,GAAG,CAAC;AAEtK,cAAc;AACd,MAAa,aAAa;IAEtB,YAAY,MAA0E;QAClF,IAAI,CAAC,MAAM,GAAG,MAAM,YAAY,sBAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,sBAAU,CAAC,MAAM,CAAC,CAAC;IACjF,CAAC;IACM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAgC,OAAO,IAAiC,CAAC,CAAC,CAAC;IAC5F,IAAI;QACP,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAAC,OAAO,6BAAa,CAAC;QAAC,CAAC;QACnE,yEAAyE;QACzE,yEAAyE;QACzE,sEAAsE;QACtE,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAAC,OAAO,6BAAa,CAAC;QAAC,CAAC;QACnE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAAC,OAAO,6BAAa,CAAC;QAAC,CAAC;QACpE,OAAa,CAA6B,CAAC;IAC/C,CAAC;IACM,KAAK,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD,WAAW,CAA0B,IAAe;QACvD,IAAI,CAA6B,CAAC;QAClC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAAC,OAAO,IAAI,CAAC;QAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,CAAC,CAAC,KAAK,CAAC;IACnB,CAAC;IACM,eAAe,CAAC,UAAkB;QACrC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YAAC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC;QAClD,MAAM,GAAG,GAAG,IAAA,wBAAY,EAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACvD,IAAI,GAAG,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1E,CAAC;QACD,mHAAmH;QACnH,8IAA8I;QAC9I,OAAO,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,CAAC;YACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACnG,CAAC;IACM,UAAU,CAAC,WAAW,GAAG,KAAK;QACjC,MAAM,IAAI,GAAG,uBAAa,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,MAAM,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,CAAC;QACjC,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IACS,kBAAkB;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAO,CAAC,CAAC;QACtC,MAAM,EAAE,GAAG,GAAG,IAAI,IAAI,wBAAU,CAAC,GAAG,CAAC,CAAC;QACtC,MAAM,GAAG,GAAG,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,SAAS,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC;QAClC,OAAO,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;IAC3C,CAAC;IACS,YAAY,CAAC,cAAsB;QACzC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,EAAE,CAAC;YAAC,OAAO,6BAAa,CAAC;QAAC,CAAC;QACnC,IAAI,GAAG,CAAC,UAAU,GAAG,cAAc,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;IACvD,CAAC;CACJ;AA7DD,sCA6DC;AAED,cAAc;AACd,MAAa,kBAAkB;IAI3B,YAAY,MAAW,EAAE,UAAmB;QACxC,IAAI,CAAC,MAAM,GAAG,MAAM,YAAY,2BAAe,CAAC,CAAC,CAAC,MAAM;YACpD,CAAC,CAAC,IAAA,wBAAY,EAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,IAAI,+BAAqB,CAAC,MAAM,EAAE,UAAW,CAAC;gBAChD,CAAC,CAAC,IAAI,2BAAe,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IACM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAqC,OAAO,IAAsC,CAAC,CAAC,CAAC;IACrG,IAAI;;YACb,IAAI,CAAC,CAAC;YACN,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBAAC,OAAO,6BAAa,CAAC;YAAC,CAAC;YACzE,yEAAyE;YACzE,yEAAyE;YACzE,sEAAsE;YACtE,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBAAC,OAAO,6BAAa,CAAC;YAAC,CAAC;YACzE,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAAC,OAAO,6BAAa,CAAC;YAAC,CAAC;YAC1E,OAAa,CAA6B,CAAC;QAC/C,CAAC;KAAA;IACY,KAAK,CAAC,KAAW;sEAAI,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAAA;IAC7D,MAAM,CAAC,KAAW;sEAAI,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAAA;IAC/D,WAAW,CAA0B,IAAe;;YAC7D,IAAI,CAA6B,CAAC;YAClC,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBAAC,OAAO,IAAI,CAAC;YAAC,CAAC;YAClD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBAChD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO,CAAC,CAAC,KAAK,CAAC;QACnB,CAAC;KAAA;IACY,eAAe,CAAC,UAAkB;;YAC3C,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;gBAAC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAAC,CAAC;YAClD,MAAM,GAAG,GAAG,IAAA,wBAAY,EAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YAC7D,IAAI,GAAG,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;YAC1E,CAAC;YACD,mHAAmH;YACnH,8IAA8I;YAC9I,OAAO,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,CAAC;gBACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACnG,CAAC;KAAA;IACY,UAAU;qEAAC,WAAW,GAAG,KAAK;YACvC,MAAM,IAAI,GAAG,uBAAa,CAAC,MAAM,CAAC;YAClC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,CAAC;YACjC,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,MAAM,CAAC;QAClB,CAAC;KAAA;IACe,kBAAkB;;YAC9B,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAO,CAAC,CAAC;YAC5C,MAAM,EAAE,GAAG,GAAG,IAAI,IAAI,wBAAU,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,GAAG,GAAG,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,SAAS,CAAC,CAAC,CAAC,KAAI,CAAC,CAAC;YAClC,OAAO,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;QAC3C,CAAC;KAAA;IACe,YAAY,CAAC,cAAsB;;YAC/C,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnD,IAAI,CAAC,GAAG,EAAE,CAAC;gBAAC,OAAO,6BAAa,CAAC;YAAC,CAAC;YACnC,IAAI,GAAG,CAAC,UAAU,GAAG,cAAc,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;YAC5E,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;QACvD,CAAC;KAAA;CACJ;AAlED,gDAkEC;AAED,cAAc;AACd,MAAa,iBAAkB,SAAQ,aAAa;IAMhD,YAAY,MAAiC;QACzC,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QANrB,YAAO,GAAG,KAAK,CAAC;QAEhB,UAAK,GAAU,EAAE,CAAC;QAClB,gBAAW,GAAG,CAAC,CAAC;QAChB,qBAAgB,GAAG,CAAC,CAAC;QAGzB,IAAI,CAAC,KAAK,GAAG,MAAM,YAAY,yBAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,yBAAS,CAAC,MAAM,CAAC,CAAC;IAC9E,CAAC;IACM,IAAI;QACP,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,MAAM,OAAO,GAAG,oBAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,uBAAa,CAAC,MAAM,CAAC,CAAC;YACrE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;QAC3C,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM,OAAO,GAAG,oBAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAa,CAAC,eAAe,CAAC,CAAC;YACvE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;QAC3C,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,oBAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAa,CAAC,WAAW,CAAC,CAAC;YACnE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;QAC3C,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,OAAO,6BAAa,CAAC;IACzB,CAAC;IACM,eAAe,CAAC,WAAoB;QACvC,OAAO,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAQ,CAAC;QAC7C,SAAS,kBAAkB,CAAC,EAAS;YACjC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAU,CAAC,OAAO,EAAE,MAAW,EAAE,EAAE,CAAC;gBACxD,GAAG,OAAO;gBACV,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;gBACrD,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;gBACnD,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;gBACjD,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC7C,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;aAC5C,EAAE,EAAa,CAAC,CAAC;QACtB,CAAC;IACL,CAAC;IACM,WAAW,CAA0B,IAAe;QACvD,IAAI,CAA6B,CAAC;QAClC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAAC,OAAO,IAAI,CAAC;QAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,CAAC,CAAC,KAAK,CAAC;IACnB,CAAC;IACM,UAAU;QACb,MAAM,IAAI,GAAG,uBAAa,CAAC,MAAM,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,MAAM,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AA9DD,8CA8DC;AAED,cAAc;AACD,QAAA,OAAO,GAAG,CAAC,CAAC;AACzB,cAAc;AACD,QAAA,SAAS,GAAG,QAAQ,CAAC;AAClC,cAAc;AACD,QAAA,KAAK,GAAG,IAAI,UAAU,CAAC,iBAAS,CAAC,MAAM,CAAC,CAAC;AAEtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3C,aAAK,CAAC,CAAC,CAAC,GAAG,iBAAS,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC;AACzC,CAAC;AAED,cAAc;AACd,SAAgB,wBAAwB,CAAC,MAAkB,EAAE,KAAK,GAAG,CAAC;IAClE,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,aAAK,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QAC1C,IAAI,aAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAPD,4DAOC;AAED,cAAc;AACD,QAAA,WAAW,GAAG,aAAK,CAAC,MAAM,CAAC;AACxC,cAAc;AACD,QAAA,eAAe,GAAG,mBAAW,GAAG,eAAO,CAAC;AACrD,cAAc;AACD,QAAA,iBAAiB,GAAG,mBAAW,GAAG,CAAC,GAAG,eAAO,CAAC", "file": "message.js", "sourceRoot": "../src"}