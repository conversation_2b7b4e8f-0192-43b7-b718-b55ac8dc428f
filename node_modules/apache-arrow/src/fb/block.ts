// automatically generated by the FlatBuffers compiler, do not modify

import * as flatbuffers from 'flatbuffers';

export class Block {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):Block {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

/**
 * Index to the start of the RecordBlock (note this is past the Message header)
 */
offset():bigint {
  return this.bb!.readInt64(this.bb_pos);
}

/**
 * Length of the metadata
 */
metaDataLength():number {
  return this.bb!.readInt32(this.bb_pos + 8);
}

/**
 * Length of the data (this is aligned so there can be a gap between this and
 * the metadata).
 */
bodyLength():bigint {
  return this.bb!.readInt64(this.bb_pos + 16);
}

static sizeOf():number {
  return 24;
}

static createBlock(builder:flatbuffers.Builder, offset: bigint, metaDataLength: number, bodyLength: bigint):flatbuffers.Offset {
  builder.prep(8, 24);
  builder.writeInt64(BigInt(bodyLength ?? 0));
  builder.pad(4);
  builder.writeInt32(metaDataLength);
  builder.writeInt64(BigInt(offset ?? 0));
  return builder.offset();
}

}
