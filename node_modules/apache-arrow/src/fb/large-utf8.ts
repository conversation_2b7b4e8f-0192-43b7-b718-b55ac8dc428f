// automatically generated by the FlatBuffers compiler, do not modify

import * as flatbuffers from 'flatbuffers';

/**
 * Same as Utf8, but with 64-bit offsets, allowing to represent
 * extremely large data values.
 */
export class LargeUtf8 {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LargeUtf8 {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLargeUtf8(bb:flatbuffers.ByteBuffer, obj?:LargeUtf8):LargeUtf8 {
  return (obj || new LargeUtf8()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLargeUtf8(bb:flatbuffers.ByteBuffer, obj?:LargeUtf8):LargeUtf8 {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LargeUtf8()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static startLargeUtf8(builder:flatbuffers.Builder) {
  builder.startObject(0);
}

static endLargeUtf8(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createLargeUtf8(builder:flatbuffers.Builder):flatbuffers.Offset {
  LargeUtf8.startLargeUtf8(builder);
  return LargeUtf8.endLargeUtf8(builder);
}
}
