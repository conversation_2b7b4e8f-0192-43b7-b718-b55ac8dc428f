import nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';
import { clientLogger } from './logger';

/**
 * Secure Key Manager - Addresses localStorage vulnerability
 * Uses password-derived key storage and WebCrypto API for enhanced security
 */

const secLogger = {
  debug: (message: string, data?: any) => {
    if (import.meta.env.DEV) {
      clientLogger.log(`[SecureKeyManager] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    clientLogger.log(`[SecureKeyManager] ${message}`, data);
  },
  warn: (message: string, error?: any) => {
    console.warn(`[SecureKeyManager Warning] ${message}`, error);
  },
  error: (message: string, error?: any) => {
    clientLogger.error(`[SecureKeyManager] ${message}`, error);
  }
};

interface SecureKeyPair {
  publicKey: Uint8Array;
  secretKey: Uint8Array;
  keyId: string;
  derivedAt: Date;
}

interface KeyDerivationParams {
  salt: Uint8Array;
  iterations: number;
  keyId: string;
}

class SecureKeyManager {
  private static instance: SecureKeyManager;
  private currentKeyPair: SecureKeyPair | null = null;
  private derivationParams: KeyDerivationParams | null = null;
  private webCryptoKey: CryptoKey | null = null;
  private sessionActive: boolean = false;

  private constructor() {
    // Private constructor for singleton pattern
  }

  public static getInstance(): SecureKeyManager {
    if (!SecureKeyManager.instance) {
      SecureKeyManager.instance = new SecureKeyManager();
    }
    return SecureKeyManager.instance;
  }

  /**
   * Initialize secure key storage with user password
   * @param password User's password for key derivation
   * @param existingParams Optional existing derivation parameters for key recovery
   */
  public async initializeWithPassword(
    password: string, 
    existingParams?: KeyDerivationParams
  ): Promise<{ publicKey: string; keyId: string }> {
    try {
      secLogger.info('Initializing secure key manager with password derivation');

      // Use existing params or generate new ones
      const params = existingParams || this.generateDerivationParams();
      this.derivationParams = params;

      // Derive encryption key from password using PBKDF2
      const passwordKey = await this.deriveKeyFromPassword(password, params);
      
      // Generate or recover NaCl key pair
      const keyPair = await this.generateSecureKeyPair(passwordKey, params.keyId);
      
      // Store in memory only
      this.currentKeyPair = keyPair;
      this.sessionActive = true;

      // Store derivation parameters (safe to store as they don't contain secrets)
      this.storeDerivationParams(params);

      secLogger.info('Secure key manager initialized successfully');
      
      return {
        publicKey: naclUtil.encodeBase64(keyPair.publicKey),
        keyId: keyPair.keyId
      };
    } catch (error) {
      secLogger.error('Failed to initialize secure key manager', error);
      throw new Error('Failed to initialize secure encryption keys');
    }
  }

  /**
   * Generate new derivation parameters
   */
  private generateDerivationParams(): KeyDerivationParams {
    return {
      salt: nacl.randomBytes(32), // 256-bit salt
      iterations: 100000, // PBKDF2 iterations (secure but reasonable performance)
      keyId: naclUtil.encodeBase64(nacl.randomBytes(16)) // Unique key identifier
    };
  }

  /**
   * Derive encryption key from password using PBKDF2
   */
  private async deriveKeyFromPassword(
    password: string, 
    params: KeyDerivationParams
  ): Promise<CryptoKey> {
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);

    // Import password as key material
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    // Derive key using PBKDF2
    const derivedKey = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: params.salt,
        iterations: params.iterations,
        hash: 'SHA-256'
      },
      keyMaterial,
      {
        name: 'AES-GCM',
        length: 256
      },
      false, // Non-extractable for security
      ['encrypt', 'decrypt']
    );

    this.webCryptoKey = derivedKey;
    return derivedKey;
  }

  /**
   * Generate secure NaCl key pair from derived key
   */
  private async generateSecureKeyPair(
    derivedKey: CryptoKey, 
    keyId: string
  ): Promise<SecureKeyPair> {
    try {
      // Check if we have existing encrypted keys to recover
      const existingEncryptedKeys = this.getEncryptedKeysFromStorage(keyId);
      
      if (existingEncryptedKeys) {
        secLogger.debug('Recovering existing key pair from encrypted storage');
        return await this.recoverKeyPairFromEncrypted(derivedKey, existingEncryptedKeys, keyId);
      } else {
        secLogger.debug('Generating new NaCl key pair');
        return await this.generateNewKeyPair(derivedKey, keyId);
      }
    } catch (error) {
      secLogger.error('Failed to generate/recover secure key pair', error);
      throw error;
    }
  }

  /**
   * Generate brand new NaCl key pair and encrypt for storage
   */
  private async generateNewKeyPair(
    derivedKey: CryptoKey, 
    keyId: string
  ): Promise<SecureKeyPair> {
    // Generate new NaCl key pair
    const naclKeyPair = nacl.box.keyPair();
    
    // Encrypt the secret key for secure storage
    await this.encryptAndStoreSecretKey(derivedKey, naclKeyPair.secretKey, keyId);
    
    return {
      publicKey: naclKeyPair.publicKey,
      secretKey: naclKeyPair.secretKey,
      keyId,
      derivedAt: new Date()
    };
  }

  /**
   * Encrypt secret key and store it securely
   */
  private async encryptAndStoreSecretKey(
    derivedKey: CryptoKey, 
    secretKey: Uint8Array, 
    keyId: string
  ): Promise<void> {
    try {
      const iv = crypto.getRandomValues(new Uint8Array(12)); // AES-GCM IV
      
      const encryptedSecretKey = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        derivedKey,
        secretKey
      );

      const encryptedData = {
        encryptedSecretKey: naclUtil.encodeBase64(new Uint8Array(encryptedSecretKey)),
        iv: naclUtil.encodeBase64(iv),
        keyId
      };

      // Store encrypted key (safe to store as it's password-protected)
      localStorage.setItem(`e2ee_encrypted_key_${keyId}`, JSON.stringify(encryptedData));
      secLogger.debug('Secret key encrypted and stored successfully');
    } catch (error) {
      secLogger.error('Failed to encrypt and store secret key', error);
      throw error;
    }
  }

  /**
   * Recover key pair from encrypted storage
   */
  private async recoverKeyPairFromEncrypted(
    derivedKey: CryptoKey, 
    encryptedData: any, 
    keyId: string
  ): Promise<SecureKeyPair> {
    try {
      const iv = naclUtil.decodeBase64(encryptedData.iv);
      const encryptedSecretKey = naclUtil.decodeBase64(encryptedData.encryptedSecretKey);

      const decryptedSecretKey = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        derivedKey,
        encryptedSecretKey
      );

      const secretKey = new Uint8Array(decryptedSecretKey);
      const naclKeyPair = nacl.box.keyPair.fromSecretKey(secretKey);

      secLogger.debug('Key pair recovered successfully from encrypted storage');

      return {
        publicKey: naclKeyPair.publicKey,
        secretKey: naclKeyPair.secretKey,
        keyId,
        derivedAt: new Date()
      };
    } catch (error) {
      secLogger.error('Failed to recover key pair from encrypted storage', error);
      throw new Error('Failed to decrypt stored keys - incorrect password or corrupted data');
    }
  }

  /**
   * Get encrypted keys from storage
   */
  private getEncryptedKeysFromStorage(keyId: string): any | null {
    try {
      const stored = localStorage.getItem(`e2ee_encrypted_key_${keyId}`);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      secLogger.error('Failed to parse encrypted keys from storage', error);
      return null;
    }
  }

  /**
   * Store derivation parameters (safe to store as they don't contain secrets)
   */
  private storeDerivationParams(params: KeyDerivationParams): void {
    try {
      const paramsData = {
        salt: naclUtil.encodeBase64(params.salt),
        iterations: params.iterations,
        keyId: params.keyId
      };
      localStorage.setItem('e2ee_derivation_params', JSON.stringify(paramsData));
    } catch (error) {
      secLogger.error('Failed to store derivation parameters', error);
    }
  }

  /**
   * Get stored derivation parameters
   */
  public getStoredDerivationParams(): KeyDerivationParams | null {
    try {
      const stored = localStorage.getItem('e2ee_derivation_params');
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      return {
        salt: naclUtil.decodeBase64(parsed.salt),
        iterations: parsed.iterations,
        keyId: parsed.keyId
      };
    } catch (error) {
      secLogger.error('Failed to parse stored derivation parameters', error);
      return null;
    }
  }

  /**
   * Get current public key
   */
  public getPublicKey(): string | null {
    if (!this.currentKeyPair || !this.sessionActive) {
      return null;
    }
    return naclUtil.encodeBase64(this.currentKeyPair.publicKey);
  }

  /**
   * Get current secret key
   */
  public getSecretKey(): string | null {
    if (!this.currentKeyPair || !this.sessionActive) {
      return null;
    }
    return naclUtil.encodeBase64(this.currentKeyPair.secretKey);
  }

  /**
   * Check if session is active and keys are available
   */
  public isSessionActive(): boolean {
    return this.sessionActive && this.currentKeyPair !== null;
  }

  /**
   * Get current key ID
   */
  public getCurrentKeyId(): string | null {
    return this.currentKeyPair?.keyId || null;
  }

  /**
   * Clear session (logout)
   */
  public clearSession(): void {
    this.currentKeyPair = null;
    this.webCryptoKey = null;
    this.sessionActive = false;
    secLogger.info('Secure session cleared');
  }

  /**
   * Check if user has existing encrypted keys
   */
  public hasExistingKeys(): boolean {
    const params = this.getStoredDerivationParams();
    if (!params) return false;

    const encryptedKeys = this.getEncryptedKeysFromStorage(params.keyId);
    return encryptedKeys !== null;
  }

  /**
   * Migrate from old localStorage keys (one-time migration)
   */
  public async migrateFromLegacyStorage(password: string): Promise<boolean> {
    try {
      const legacySecretKey = localStorage.getItem('e2ee_secret_key');
      if (!legacySecretKey) {
        return false; // No legacy keys to migrate
      }

      secLogger.info('Migrating from legacy localStorage keys');

      // Initialize new secure storage
      const { keyId } = await this.initializeWithPassword(password);

      // Remove legacy storage
      localStorage.removeItem('e2ee_secret_key');
      
      secLogger.info('Legacy key migration completed successfully');
      return true;
    } catch (error) {
      secLogger.error('Failed to migrate legacy keys', error);
      return false;
    }
  }
}

export default SecureKeyManager;
export { SecureKeyManager };