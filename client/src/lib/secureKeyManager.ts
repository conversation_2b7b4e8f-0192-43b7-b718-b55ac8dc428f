import nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';
import { clientLogger } from './logger';

/**
 * Secure Key Manager - Enhanced security implementation
 * Uses IndexedDB with encryption and WebCrypto API for enhanced security
 * Addresses localStorage vulnerability by implementing secure storage with XSS protection
 */

// IndexedDB configuration
const DB_NAME = 'CrowAISecureKeys';
const DB_VERSION = 1;
const STORE_NAME = 'encryptedKeys';

interface IndexedDBKey {
  keyId: string;
  encryptedSecretKey: string;
  iv: string;
  timestamp: number;
  version: string;
}

interface SecureStorageConfig {
  dbName: string;
  version: number;
  storeName: string;
}

const secLogger = {
  debug: (message: string, data?: any) => {
    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {
      clientLogger.log(`[SecureKeyManager] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    clientLogger.log(`[SecureKeyManager] ${message}`, data);
  },
  warn: (message: string, error?: any) => {
    console.warn(`[SecureKeyManager Warning] ${message}`, error);
  },
  error: (message: string, error?: any) => {
    clientLogger.error(`[SecureKeyManager] ${message}`, error);
  }
};

interface SecureKeyPair {
  publicKey: Uint8Array;
  secretKey: Uint8Array;
  keyId: string;
  derivedAt: Date;
}

interface KeyDerivationParams {
  salt: Uint8Array;
  iterations: number;
  keyId: string;
}

class SecureKeyManager {
  private static instance: SecureKeyManager;
  private currentKeyPair: SecureKeyPair | null = null;
  private derivationParams: KeyDerivationParams | null = null;
  private webCryptoKey: CryptoKey | null = null;
  private sessionActive: boolean = false;
  private db: IDBDatabase | null = null;
  private storageConfig: SecureStorageConfig = {
    dbName: DB_NAME,
    version: DB_VERSION,
    storeName: STORE_NAME
  };

  private constructor() {
    // Private constructor for singleton pattern
    this.initializeIndexedDB().catch(error => {
      secLogger.error('Failed to initialize IndexedDB', error);
    });
  }

  public static getInstance(): SecureKeyManager {
    if (!SecureKeyManager.instance) {
      SecureKeyManager.instance = new SecureKeyManager();
    }
    return SecureKeyManager.instance;
  }

  /**
   * Initialize IndexedDB for secure key storage
   */
  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.storageConfig.dbName, this.storageConfig.version);
      
      request.onerror = () => {
        secLogger.error('Failed to open IndexedDB');
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        secLogger.info('IndexedDB initialized successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object store if it doesn't exist
        if (!db.objectStoreNames.contains(this.storageConfig.storeName)) {
          const store = db.createObjectStore(this.storageConfig.storeName, { keyPath: 'keyId' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('version', 'version', { unique: false });
        }
      };
    });
  }

  /**
   * Get IndexedDB transaction for secure operations
   */
  private async getDBTransaction(mode: IDBTransactionMode = 'readonly'): Promise<IDBObjectStore> {
    if (!this.db) {
      await this.initializeIndexedDB();
    }

    if (!this.db) {
      throw new Error('IndexedDB not available');
    }

    const transaction = this.db.transaction([this.storageConfig.storeName], mode);
    return transaction.objectStore(this.storageConfig.storeName);
  }

  /**
   * Enhanced secure storage using IndexedDB with encryption
   */
  private async encryptAndStoreSecretKeySecure(
    derivedKey: CryptoKey,
    secretKey: Uint8Array,
    keyId: string
  ): Promise<void> {
    try {
      const iv = crypto.getRandomValues(new Uint8Array(12)); // AES-GCM IV
      
      // Convert Uint8Array to ArrayBuffer for Web Crypto API
      const secretKeyBuffer = secretKey.buffer.slice(secretKey.byteOffset, secretKey.byteOffset + secretKey.byteLength) as ArrayBuffer;

      const encryptedSecretKey = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        derivedKey,
        secretKeyBuffer
      );

      const encryptedData: IndexedDBKey = {
        keyId,
        encryptedSecretKey: naclUtil.encodeBase64(new Uint8Array(encryptedSecretKey)),
        iv: naclUtil.encodeBase64(iv),
        timestamp: Date.now(),
        version: '1.0'
      };

      // Store in IndexedDB instead of localStorage
      const store = await this.getDBTransaction('readwrite');
      await store.put(encryptedData);
      
      secLogger.debug('Secret key encrypted and stored securely in IndexedDB');
    } catch (error) {
      secLogger.error('Failed to encrypt and store secret key securely', error);
      throw error;
    }
  }

  /**
   * Retrieve encrypted keys from IndexedDB
   */
  private async getEncryptedKeysFromStorageSecure(keyId: string): Promise<IndexedDBKey | null> {
    try {
      const store = await this.getDBTransaction('readonly');
      const request = store.get(keyId);
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          resolve(request.result || null);
        };
        request.onerror = () => {
          secLogger.error('Failed to retrieve encrypted keys from storage', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      secLogger.error('Failed to get encrypted keys from secure storage', error);
      return null;
    }
  }

  /**
   * Clean up old keys from IndexedDB (key rotation)
   */
  private async cleanupOldKeys(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const store = await this.getDBTransaction('readwrite');
      const index = store.index('timestamp');
      const request = index.openCursor(IDBKeyRange.upperBound(Date.now() - maxAge));
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            store.delete(cursor.primaryKey);
            cursor.continue();
          } else {
            secLogger.debug('Old keys cleanup completed');
            resolve();
          }
        };
        request.onerror = () => {
          secLogger.error('Failed to cleanup old keys', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      secLogger.error('Failed to cleanup old keys', error);
      // Don't throw error as this is a maintenance operation
    }
  }

  /**
   * Check for XSS attempts and secure key material
   */
  private validateSecurityContext(): boolean {
    // Check if we're in a secure context
    if (!window.isSecureContext) {
      secLogger.warn('Not running in secure context - potential security risk');
      return false;
    }

    // Check for suspicious global properties that might indicate XSS
    const suspiciousGlobals = ['__defineGetter__', '__defineSetter__', '__lookupGetter__', '__lookupSetter__'];
    for (const global of suspiciousGlobals) {
      if (typeof (window as any)[global] === 'function') {
        secLogger.warn(`Suspicious global property detected: ${global}`);
        return false;
      }
    }

    // Check document URL for suspicious protocols
    if (document.location.protocol !== 'https:' && document.location.protocol !== 'http:') {
      secLogger.warn(`Suspicious document protocol: ${document.location.protocol}`);
      return false;
    }

    return true;
  }

  /**
   * Generate secure random salt with enhanced entropy
   */
  private generateSecureSalt(): Uint8Array {
    // Use Web Crypto API for better entropy
    const salt = crypto.getRandomValues(new Uint8Array(32));
    
    // Mix in some additional entropy from performance timing
    const timingEntropy = performance.now();
    const timingBuffer = new Uint8Array(8);
    for (let i = 0; i < 8; i++) {
      timingBuffer[i] = (timingEntropy >> (i * 8)) & 0xff;
    }
    
    // Combine entropy sources
    const combinedEntropy = new Uint8Array(40);
    combinedEntropy.set(salt, 0);
    combinedEntropy.set(timingBuffer, 32);
    
    return combinedEntropy;
  }

  /**
   * Enhanced key derivation with additional security measures
   */
  private async deriveKeyFromPasswordEnhanced(
    password: string,
    params: KeyDerivationParams
  ): Promise<CryptoKey> {
    // Validate password strength
    if (password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }

    // Additional entropy mixing
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);
    
    // Import password as key material
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );

    // Enhanced key derivation with additional iterations for security
    const derivedKey = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: params.salt as BufferSource,
        iterations: params.iterations + 50000, // Additional iterations for security
        hash: 'SHA-256'
      },
      keyMaterial,
      {
        name: 'AES-GCM',
        length: 256
      },
      false, // Non-extractable for security
      ['encrypt', 'decrypt']
    );

    this.webCryptoKey = derivedKey;
    return derivedKey;
  }

  /**
   * Initialize secure key storage with user password
   * @param password User's password for key derivation
   * @param existingParams Optional existing derivation parameters for key recovery
   */
  public async initializeWithPassword(
    password: string,
    existingParams?: KeyDerivationParams
  ): Promise<{ publicKey: string; keyId: string }> {
    try {
      // Validate security context
      if (!this.validateSecurityContext()) {
        throw new Error('Security context validation failed');
      }

      secLogger.info('Initializing secure key manager with enhanced security');

      // Use existing params or generate new ones with enhanced security
      const params = existingParams || this.generateDerivationParamsEnhanced();
      this.derivationParams = params;

      // Enhanced key derivation
      const passwordKey = await this.deriveKeyFromPasswordEnhanced(password, params);
      
      // Generate or recover NaCl key pair with enhanced security
      const keyPair = await this.generateSecureKeyPairEnhanced(passwordKey, params.keyId);
      
      // Store in memory only
      this.currentKeyPair = keyPair;
      this.sessionActive = true;

      // Store derivation parameters (safe to store as they don't contain secrets)
      await this.storeDerivationParamsSecure(params);

      // Perform key rotation cleanup
      await this.cleanupOldKeys();

      secLogger.info('Secure key manager initialized with enhanced security');
      
      return {
        publicKey: naclUtil.encodeBase64(keyPair.publicKey),
        keyId: keyPair.keyId
      };
    } catch (error) {
      secLogger.error('Failed to initialize secure key manager with enhanced security', error);
      throw new Error('Failed to initialize secure encryption keys');
    }
  }

  /**
   * Generate enhanced derivation parameters with additional security
   */
  private generateDerivationParamsEnhanced(): KeyDerivationParams {
    return {
      salt: this.generateSecureSalt(), // Enhanced salt generation
      iterations: 200000, // Increased iterations for better security
      keyId: naclUtil.encodeBase64(nacl.randomBytes(16)) // Unique key identifier
    };
  }

  /**
   * Generate secure NaCl key pair from derived key with enhanced security
   */
  private async generateSecureKeyPairEnhanced(
    derivedKey: CryptoKey,
    keyId: string
  ): Promise<SecureKeyPair> {
    try {
      // Check if we have existing encrypted keys to recover
      const existingEncryptedKeys = await this.getEncryptedKeysFromStorageSecure(keyId);
      
      if (existingEncryptedKeys) {
        secLogger.debug('Recovering existing key pair from secure encrypted storage');
        return await this.recoverKeyPairFromEncryptedEnhanced(derivedKey, existingEncryptedKeys, keyId);
      } else {
        secLogger.debug('Generating new NaCl key pair with enhanced security');
        return await this.generateNewKeyPairEnhanced(derivedKey, keyId);
      }
    } catch (error) {
      secLogger.error('Failed to generate/recover secure key pair with enhanced security', error);
      throw error;
    }
  }

  /**
   * Generate brand new NaCl key pair and encrypt for storage with enhanced security
   */
  private async generateNewKeyPairEnhanced(
    derivedKey: CryptoKey,
    keyId: string
  ): Promise<SecureKeyPair> {
    // Generate new NaCl key pair
    const naclKeyPair = nacl.box.keyPair();
    
    // Encrypt the secret key for secure storage using enhanced method
    await this.encryptAndStoreSecretKeySecure(derivedKey, naclKeyPair.secretKey, keyId);
    
    return {
      publicKey: naclKeyPair.publicKey,
      secretKey: naclKeyPair.secretKey,
      keyId,
      derivedAt: new Date()
    };
  }

  /**
   * Recover key pair from encrypted storage with enhanced security
   */
  private async recoverKeyPairFromEncryptedEnhanced(
    derivedKey: CryptoKey,
    encryptedData: IndexedDBKey,
    keyId: string
  ): Promise<SecureKeyPair> {
    try {
      const iv = naclUtil.decodeBase64(encryptedData.iv);
      const encryptedSecretKey = naclUtil.decodeBase64(encryptedData.encryptedSecretKey);

      // Convert ArrayBuffer to Uint8Array for decryption
      const encryptedSecretKeyBuffer = encryptedSecretKey.buffer.slice(
        encryptedSecretKey.byteOffset,
        encryptedSecretKey.byteOffset + encryptedSecretKey.byteLength
      ) as ArrayBuffer;

      const decryptedSecretKeyBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv as BufferSource
        },
        derivedKey,
        encryptedSecretKeyBuffer
      );

      const secretKey = new Uint8Array(decryptedSecretKeyBuffer);
      const naclKeyPair = nacl.box.keyPair.fromSecretKey(secretKey);

      secLogger.debug('Key pair recovered successfully from enhanced secure storage');

      return {
        publicKey: naclKeyPair.publicKey,
        secretKey: naclKeyPair.secretKey,
        keyId,
        derivedAt: new Date()
      };
    } catch (error) {
      secLogger.error('Failed to recover key pair from enhanced secure storage', error);
      throw new Error('Failed to decrypt stored keys - incorrect password or corrupted data');
    }
  }

  /**
   * Store derivation parameters securely (safe to store as they don't contain secrets)
   */
  private async storeDerivationParamsSecure(params: KeyDerivationParams): Promise<void> {
    try {
      const paramsData = {
        salt: naclUtil.encodeBase64(params.salt),
        iterations: params.iterations,
        keyId: params.keyId
      };
      
      // Use localStorage for non-sensitive parameters only
      localStorage.setItem('e2ee_derivation_params', JSON.stringify(paramsData));
    } catch (error) {
      secLogger.error('Failed to store derivation parameters securely', error);
      throw error;
    }
  }

  /**
   * Get stored derivation parameters
   */
  public getStoredDerivationParams(): KeyDerivationParams | null {
    try {
      const stored = localStorage.getItem('e2ee_derivation_params');
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      return {
        salt: naclUtil.decodeBase64(parsed.salt),
        iterations: parsed.iterations,
        keyId: parsed.keyId
      };
    } catch (error) {
      secLogger.error('Failed to parse stored derivation parameters', error);
      return null;
    }
  }

  /**
   * Get current public key
   */
  public getPublicKey(): string | null {
    if (!this.currentKeyPair || !this.sessionActive) {
      return null;
    }
    return naclUtil.encodeBase64(this.currentKeyPair.publicKey);
  }

  /**
   * Get current secret key
   */
  public getSecretKey(): string | null {
    if (!this.currentKeyPair || !this.sessionActive) {
      return null;
    }
    return naclUtil.encodeBase64(this.currentKeyPair.secretKey);
  }

  /**
   * Check if session is active and keys are available
   */
  public isSessionActive(): boolean {
    return this.sessionActive && this.currentKeyPair !== null;
  }

  /**
   * Get current key ID
   */
  public getCurrentKeyId(): string | null {
    return this.currentKeyPair?.keyId || null;
  }

  /**
   * Clear session (logout)
   */
  public clearSession(): void {
    this.currentKeyPair = null;
    this.webCryptoKey = null;
    this.sessionActive = false;
    secLogger.info('Secure session cleared with enhanced security');
  }

  /**
   * Check if user has existing encrypted keys
   */
  public async hasExistingKeys(): Promise<boolean> {
    const params = this.getStoredDerivationParams();
    if (!params) return false;

    const encryptedKeys = await this.getEncryptedKeysFromStorageSecure(params.keyId);
    return encryptedKeys !== null;
  }

  /**
   * Migrate from old localStorage keys to secure IndexedDB storage
   */
  public async migrateFromLegacyStorage(password: string): Promise<boolean> {
    try {
      const legacySecretKey = localStorage.getItem('e2ee_secret_key');
      if (!legacySecretKey) {
        return false; // No legacy keys to migrate
      }

      secLogger.info('Migrating from legacy localStorage keys to secure IndexedDB');

      // Initialize new secure storage
      const { keyId } = await this.initializeWithPassword(password);

      // Remove legacy storage
      localStorage.removeItem('e2ee_secret_key');
      
      secLogger.info('Legacy key migration to secure storage completed successfully');
      return true;
    } catch (error) {
      secLogger.error('Failed to migrate legacy keys to secure storage', error);
      return false;
    }
  }

  /**
   * Rotate encryption keys for enhanced security
   */
  public async rotateEncryptionKeys(password: string): Promise<{ publicKey: string; keyId: string }> {
    try {
      secLogger.info('Starting encryption key rotation');

      // Clear current session
      this.clearSession();

      // Generate new derivation parameters
      const newParams = this.generateDerivationParamsEnhanced();
      
      // Initialize with new parameters
      const result = await this.initializeWithPassword(password, newParams);

      secLogger.info('Encryption key rotation completed successfully');
      return result;
    } catch (error) {
      secLogger.error('Failed to rotate encryption keys', error);
      throw new Error('Failed to rotate encryption keys');
    }
  }

  /**
   * Get storage statistics for monitoring
   */
  public async getStorageStats(): Promise<{
    hasKeys: boolean;
    keyCount: number;
    lastRotation?: Date;
    storageType: 'indexeddb' | 'localStorage';
  }> {
    try {
      const params = this.getStoredDerivationParams();
      let keyCount = 0;
      
      if (params) {
        const encryptedKeys = await this.getEncryptedKeysFromStorageSecure(params.keyId);
        keyCount = encryptedKeys ? 1 : 0;
      }

      return {
        hasKeys: keyCount > 0,
        keyCount,
        storageType: 'indexeddb',
      };
    } catch (error) {
      secLogger.error('Failed to get storage stats', error);
      return {
        hasKeys: false,
        keyCount: 0,
        storageType: 'indexeddb',
      };
    }
  }
}

export default SecureKeyManager;
export { SecureKeyManager };