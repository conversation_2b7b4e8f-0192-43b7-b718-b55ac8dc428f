import * as nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';

// Storage interface
export interface SecureStorage {
  set(key: string, value: any): Promise<void>;
  get(key: string): Promise<any>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
  keys(): Promise<string[]>;
}

// Memory-based secure storage (for development/testing)
export class MemorySecureStorage implements SecureStorage {
  private storage: Map<string, string> = new Map();
  private encryptionKey: string;

  constructor(encryptionKey: string) {
    this.encryptionKey = encryptionKey;
  }

  private encryptData(data: string): string {
    // Simple XOR encryption for demonstration
    // In production, use proper encryption like AES
    const key = this.encryptionKey.padEnd(32, '0').substring(0, 32);
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(result);
  }

  private decryptData(encrypted: string): string {
    try {
      const data = atob(encrypted);
      const key = this.encryptionKey.padEnd(32, '0').substring(0, 32);
      let result = '';
      for (let i = 0; i < data.length; i++) {
        result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return result;
    } catch (error) {
      throw new Error('Failed to decrypt data');
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      const encrypted = this.encryptData(serialized);
      this.storage.set(key, encrypted);
    } catch (error) {
      console.error('Failed to store value:', error);
      throw new Error('Failed to store value securely');
    }
  }

  async get(key: string): Promise<any> {
    try {
      const encrypted = this.storage.get(key);
      if (!encrypted) {
        return null;
      }

      const decrypted = this.decryptData(encrypted);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Failed to retrieve value:', error);
      throw new Error('Failed to retrieve value securely');
    }
  }

  async remove(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }

  async has(key: string): Promise<boolean> {
    return this.storage.has(key);
  }

  async keys(): Promise<string[]> {
    return Array.from(this.storage.keys());
  }
}

// Session storage wrapper
export class SessionStorageWrapper implements SecureStorage {
  private encryptionKey: string;

  constructor(encryptionKey: string) {
    this.encryptionKey = encryptionKey;
  }

  private encryptData(data: string): string {
    const key = this.encryptionKey.padEnd(32, '0').substring(0, 32);
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(result);
  }

  private decryptData(encrypted: string): string {
    try {
      const data = atob(encrypted);
      const key = this.encryptionKey.padEnd(32, '0').substring(0, 32);
      let result = '';
      for (let i = 0; i < data.length; i++) {
        result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return result;
    } catch (error) {
      throw new Error('Failed to decrypt data');
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      const encrypted = this.encryptData(serialized);
      sessionStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to store value in session:', error);
      throw new Error('Failed to store value securely');
    }
  }

  async get(key: string): Promise<any> {
    try {
      const encrypted = sessionStorage.getItem(key);
      if (!encrypted) {
        return null;
      }

      const decrypted = this.decryptData(encrypted);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Failed to retrieve value from session:', error);
      throw new Error('Failed to retrieve value securely');
    }
  }

  async remove(key: string): Promise<void> {
    sessionStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    sessionStorage.clear();
  }

  async has(key: string): Promise<boolean> {
    return sessionStorage.getItem(key) !== null;
  }

  async keys(): Promise<string[]> {
    const keys: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key) {
        keys.push(key);
      }
    }
    return keys;
  }
}

// Local storage wrapper with encryption
export class LocalStorageWrapper implements SecureStorage {
  private encryptionKey!: string;
  private isAvailable: boolean;

  constructor(encryption: EncryptionService) {
    this.encryptionKey = encryptionKey;
    this.isAvailable = typeof localStorage !== 'undefined';
  }

  async set(key: string, value: any): Promise<void> {
    if (!this.isAvailable) {
      throw new Error('Local storage is not available');
    }

    try {
      const serialized = JSON.stringify(value);
      const encrypted = this.encryptData(serialized);
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to store value in local storage:', error);
      throw new Error('Failed to store value securely');
    }
  }

  async get(key: string): Promise<any> {
    if (!this.isAvailable) {
      throw new Error('Local storage is not available');
    }

    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) {
        return null;
      }

      const decrypted = this.decryptData(encrypted);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Failed to retrieve value from local storage:', error);
      throw new Error('Failed to retrieve value securely');
    }
  }

  async remove(key: string): Promise<void> {
    if (!this.isAvailable) {
      throw new Error('Local storage is not available');
    }
    localStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    if (!this.isAvailable) {
      throw new Error('Local storage is not available');
    }
    localStorage.clear();
  }

  async has(key: string): Promise<boolean> {
    if (!this.isAvailable) {
      return false;
    }
    return localStorage.getItem(key) !== null;
  }

  async keys(): Promise<string[]> {
    if (!this.isAvailable) {
      return [];
    }

    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        keys.push(key);
      }
    }
    return keys;
  }
}

// Secure storage factory
export class SecureStorageFactory {
  private encryptionKey: string;

  constructor(encryptionKey: string) {
    this.encryptionKey = encryptionKey;
  }

  private encryptData(data: string): string {
    const key = this.encryptionKey.padEnd(32, '0').substring(0, 32);
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(result);
  }

  private decryptData(encrypted: string): string {
    try {
      const data = atob(encrypted);
      const key = this.encryptionKey.padEnd(32, '0').substring(0, 32);
      let result = '';
      for (let i = 0; i < data.length; i++) {
        result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return result;
    } catch (error) {
      throw new Error('Failed to decrypt data');
    }
  }

  // Create appropriate storage based on environment and security requirements
  createStorage(type: 'memory' | 'session' | 'local' = 'session'): SecureStorage {
    switch (type) {
      case 'memory':
        return new MemorySecureStorage(this.encryptionKey);
      case 'session':
        return new SessionStorageWrapper(this.encryptionKey);
      case 'local':
        return new LocalStorageWrapper(this.encryptionKey);
      default:
        return new SessionStorageWrapper(this.encryptionKey);
    }
  }

  // Create storage with fallback mechanism
  createResilientStorage(preferredType: 'session' | 'local' = 'session'): SecureStorage {
    try {
      // Try preferred type first
      const preferred = this.createStorage(preferredType);
      
      // Test if it works
      preferred.set('test', 'value').catch(() => {
        throw new Error('Preferred storage not available');
      });
      
      return preferred;
    } catch (error) {
      console.warn(`Preferred storage (${preferredType}) not available, falling back to session storage`);
      return this.createStorage('session');
    }
  }
}

// Secure storage manager with advanced features
export class SecureStorageManager {
  private storage: SecureStorage;
  private encryptionKey!: string;
  private prefix: string;
  private version: string;

  constructor(
    storage: SecureStorage,
    encryptionKey: string,
    prefix: string = 'secure_',
    version: string = '1.0'
  ) {
    this.storage = storage;
    this.encryptionKey = encryptionKey;
    this.prefix = prefix;
    this.version = version;
  }

  // Set a value with expiration
  async setWithExpiry(key: string, value: any, ttl: number): Promise<void> {
    const expiryTime = Date.now() + ttl;
    const data = {
      value,
      expiry: expiryTime,
      version: this.version
    };
    
    await this.storage.set(this.prefix + key, data);
  }

  // Get a value with automatic expiration check
  async getWithExpiry(key: string): Promise<{ value: any; expired: boolean }> {
    const data = await this.storage.get(this.prefix + key);
    
    if (!data) {
      return { value: null, expired: true };
    }

    if (data.expiry && Date.now() > data.expiry) {
      await this.storage.remove(this.prefix + key);
      return { value: null, expired: true };
    }

    return { value: data.value, expired: false };
  }

  // Set a value with version control
  async setWithVersion(key: string, value: any, expectedVersion?: string): Promise<boolean> {
    const currentData = await this.storage.get(this.prefix + key);
    
    if (expectedVersion && currentData?.version !== expectedVersion) {
      return false; // Version mismatch
    }

    const data = {
      value,
      version: this.version,
      timestamp: Date.now()
    };
    
    await this.storage.set(this.prefix + key, data);
    return true;
  }

  // Get a value with version check
  async getWithVersion(key: string, expectedVersion?: string): Promise<any> {
    const data = await this.storage.get(this.prefix + key);
    
    if (!data) {
      return null;
    }

    if (expectedVersion && data.version !== expectedVersion) {
      return null; // Version mismatch
    }

    return data.value;
  }

  // Set a value with access tracking
  async setWithAccessTracking(key: string, value: any): Promise<void> {
    const data = {
      value,
      accessed: 0,
      lastAccessed: Date.now(),
      version: this.version
    };
    
    await this.storage.set(this.prefix + key, data);
  }

  // Get a value with access tracking
  async getWithAccessTracking(key: string): Promise<any> {
    const data = await this.storage.get(this.prefix + key);
    
    if (!data) {
      return null;
    }

    // Update access tracking
    data.accessed++;
    data.lastAccessed = Date.now();
    
    await this.storage.set(this.prefix + key, data);
    return data.value;
  }

  // Clean expired items
  async cleanExpired(): Promise<number> {
    const keys = await this.storage.keys();
    let cleanedCount = 0;

    for (const key of keys) {
      if (key.startsWith(this.prefix)) {
        const data = await this.storage.get(key);
        
        if (data?.expiry && Date.now() > data.expiry) {
          await this.storage.remove(key);
          cleanedCount++;
        }
      }
    }

    return cleanedCount;
  }

  // Get storage statistics
  async getStats(): Promise<{
    totalItems: number;
    totalSize: number;
    averageAccessCount: number;
    oldestItem?: Date;
    newestItem?: Date;
  }> {
    const keys = await this.storage.keys();
    const prefixedKeys = keys.filter(key => key.startsWith(this.prefix));
    
    let totalAccessCount = 0;
    let oldestTime = Infinity;
    let newestTime = 0;

    for (const key of prefixedKeys) {
      const data = await this.storage.get(key);
      
      if (data?.accessed) {
        totalAccessCount += data.accessed;
        
        if (data.timestamp) {
          oldestTime = Math.min(oldestTime, data.timestamp);
          newestTime = Math.max(newestTime, data.timestamp);
        }
      }
    }

    return {
      totalItems: prefixedKeys.length,
      totalSize: prefixedKeys.length * 100, // Rough estimate
      averageAccessCount: prefixedKeys.length > 0 ? totalAccessCount / prefixedKeys.length : 0,
      oldestItem: oldestTime !== Infinity ? new Date(oldestTime) : undefined,
      newestItem: newestTime > 0 ? new Date(newestTime) : undefined
    };
  }

  // Export data (encrypted)
  async exportData(): Promise<string> {
    const keys = await this.storage.keys();
    const data: any = {};

    for (const key of keys) {
      if (key.startsWith(this.prefix)) {
        const value = await this.storage.get(key);
        data[key] = value;
      }
    }

    const serialized = JSON.stringify(data);
    return this.encryptData(serialized);
  }

  // Import data (encrypted)
  async importData(encryptedData: string): Promise<void> {
    try {
      const decrypted = this.decryptData(encryptedData);
      const data = JSON.parse(decrypted);
      
      for (const [key, value] of Object.entries(data)) {
        await this.storage.set(key, value);
      }
    } catch (error) {
      console.error('Failed to import data:', error);
      throw new Error('Failed to import data securely');
    }
  }

  // Basic storage operations
  async set(key: string, value: any): Promise<void> {
    await this.storage.set(key, value);
  }

  async get(key: string): Promise<any> {
    return await this.storage.get(key);
  }

  async remove(key: string): Promise<void> {
    await this.storage.remove(key);
  }

  async clear(): Promise<void> {
    await this.storage.clear();
  }
}

// Create global instances
let storageManager: SecureStorageManager | null = null;

export const getSecureStorage = (encryptionKey: string): SecureStorageManager => {
  if (!storageManager) {
    const factory = new SecureStorageFactory(encryptionKey);
    const storage = factory.createResilientStorage('session');
    storageManager = new SecureStorageManager(storage, encryptionKey);
  }
  
  return storageManager;
};

// Convenience functions for common operations
export const setSecureItem = async (key: string, value: any, encryptionKey: string): Promise<void> => {
  const manager = getSecureStorage(encryptionKey);
  await manager.setWithExpiry(key, value, 30 * 24 * 60 * 60 * 1000); // 30 days expiry
};

export const getSecureItem = async (key: string, encryptionKey: string): Promise<any> => {
  const manager = getSecureStorage(encryptionKey);
  const result = await manager.getWithExpiry(key);
  return result.value;
};

export const removeSecureItem = async (key: string, encryptionKey: string): Promise<void> => {
  const manager = getSecureStorage(encryptionKey);
  await manager.remove(key);
};

export const clearSecureStorage = async (encryptionKey: string): Promise<void> => {
  const manager = getSecureStorage(encryptionKey);
  await manager.clear();
};