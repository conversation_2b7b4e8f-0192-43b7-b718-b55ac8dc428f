import { Express } from "express";
import { supabase, supabaseAdmin, getUserByAuthId, createUserWithAuthId, handleSupabaseError } from "./supabase";
import { User as SelectUser } from "@shared/schema";
import cors from "cors";

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

// Middleware to verify Supabase JWT token
export async function verifySupabaseToken(req: any, res: any, next: any) {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'No token provided' });
  }

  const token = authHeader.substring(7);

  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ message: 'Invalid token' });
    }

    // Get the user from our database
    const dbUser = await getUserByAuthId(user.id);
    if (!dbUser) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check ban/suspension status
    if (dbUser.banned_at) {
      return res.status(403).json({
        message: "Account banned",
        reason: dbUser.ban_reason
      });
    }

    if (dbUser.suspended_until && new Date(dbUser.suspended_until) > new Date()) {
      return res.status(403).json({
        message: "Account suspended",
        until: dbUser.suspended_until,
        reason: dbUser.ban_reason
      });
    }

    req.user = dbUser;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(401).json({ message: 'Token verification failed' });
  }
}

export function setupAuth(app: Express) {
  const isDev = process.env.NODE_ENV !== "production";

  // CORS configuration for Supabase Auth
  app.use(cors({
    origin: isDev ? 'http://localhost:5173' : true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }));

  // Auth routes for Supabase integration

  // Login with email/password using Supabase Auth
  app.post("/api/login", async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: "Email and password are required" });
      }

      console.log('[Auth] Processing login request for:', email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.log('[Auth] Login failed:', error.message);
        return res.status(401).json({ message: error.message });
      }

      if (!data.user) {
        return res.status(401).json({ message: "Authentication failed" });
      }

      // Get user from our database
      const dbUser = await getUserByAuthId(data.user.id);
      if (!dbUser) {
        return res.status(401).json({ message: "User not found" });
      }

      // Check ban/suspension status
      if (dbUser.banned_at) {
        return res.status(403).json({
          message: "Account banned",
          reason: dbUser.ban_reason
        });
      }

      if (dbUser.suspended_until && new Date(dbUser.suspended_until) > new Date()) {
        return res.status(403).json({
          message: "Account suspended",
          until: dbUser.suspended_until,
          reason: dbUser.ban_reason
        });
      }

      // Update login info
      await supabaseAdmin
        .from('users')
        .update({
          last_login: new Date().toISOString(),
          login_count: dbUser.login_count + 1
        })
        .eq('id', dbUser.id);

      console.log(`[Auth] User logged in successfully: ${dbUser.id}`);
      res.json({
        user: dbUser,
        session: data.session
      });
    } catch (error) {
      console.error('[Auth] Login error:', error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Register with email/password using Supabase Auth
  app.post("/api/register", async (req, res) => {
    try {
      const { email, password, username } = req.body;

      if (!email || !password || !username) {
        return res.status(400).json({ message: "Email, password, and username are required" });
      }

      console.log('[Auth] Processing registration request for:', email);

      // Check if username already exists
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('id')
        .eq('username', username)
        .single();

      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Create auth user in Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username
          }
        }
      });

      if (error) {
        console.log('[Auth] Registration failed:', error.message);
        return res.status(400).json({ message: error.message });
      }

      if (!data.user) {
        return res.status(400).json({ message: "Registration failed" });
      }

      // Create user in our database
      const dbUser = await createUserWithAuthId({
        username,
        password: '', // Not needed with Supabase Auth
        is_admin: false,
        bio: '',
        auto_encryption_enabled: true,
        created_at: new Date().toISOString(),
        last_password_change: new Date().toISOString(),
        login_count: 0,
        auth_id: data.user.id
      });

      console.log(`[Auth] User registered successfully: ${dbUser.id}`);
      res.status(201).json({
        user: dbUser,
        session: data.session
      });
    } catch (error) {
      console.error('[Auth] Registration error:', error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Logout using Supabase Auth
  app.post("/api/logout", async (req, res) => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[Auth] Logout error:', error);
        return res.status(500).json({ message: "Logout failed" });
      }
      res.sendStatus(200);
    } catch (error) {
      console.error('[Auth] Logout error:', error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get current user (requires token in Authorization header)
  app.get("/api/user", verifySupabaseToken, (req, res) => {
    res.json(req.user);
  });
}