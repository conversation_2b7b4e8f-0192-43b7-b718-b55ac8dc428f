import { Express } from "express";
import { supabase, supabaseAdmin, getUserByAuthId, createUserWithAuthId, handleSupabaseError } from "./supabase";
import { User as SelectUser } from "@shared/schema";
import cors from "cors";

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

// Middleware to verify Supabase JWT token with enhanced security
export async function verifySupabaseToken(req: any, res: any, next: any) {
  const authHeader = req.headers.authorization;

  // Enhanced header validation - strict format checking
  if (!authHeader || typeof authHeader !== 'string') {
    return res.status(401).json({ message: 'Invalid authorization header format' });
  }

  // Strict Bearer token validation with proper format
  const bearerRegex = /^Bearer [A-Za-z0-9\-_]+\.?[A-Za-z0-9\-_]*\.?[A-Za-z0-9\-_]*$/;
  if (!bearerRegex.test(authHeader)) {
    return res.status(401).json({ message: 'Invalid token format' });
  }

  // Extract token with proper validation
  const tokenParts = authHeader.split(' ');
  if (tokenParts.length !== 2 || tokenParts[0] !== 'Bearer') {
    return res.status(401).json({ message: 'Invalid authorization header format' });
  }

  const token = tokenParts[1];

  // Basic token format validation
  if (!token || token.length < 10) {
    return res.status(401).json({ message: 'Invalid token length' });
  }

  try {
    // Enhanced JWT verification with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error) {
      // Log specific error types for debugging without exposing details
      const errorType = error.message || 'Unknown error';
      console.error(`[Auth] JWT verification failed: ${errorType}`);
      return res.status(401).json({ message: 'Invalid authentication token' });
    }

    if (!user) {
      return res.status(401).json({ message: 'Authentication token expired' });
    }

    // Additional JWT validation - check token structure
    try {
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));
      
      // Validate essential JWT claims
      if (!tokenPayload.exp || !tokenPayload.iat) {
        return res.status(401).json({ message: 'Invalid token claims' });
      }

      // Check if token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (tokenPayload.exp < currentTime) {
        return res.status(401).json({ message: 'Authentication token expired' });
      }

      // Check if token was issued in the future (potential replay attack)
      if (tokenPayload.iat > currentTime + 300) { // 5 minute tolerance
        return res.status(401).json({ message: 'Invalid token issuance time' });
      }

      // Validate user ID format
      if (!user.id || typeof user.id !== 'string' || user.id.length < 10) {
        return res.status(401).json({ message: 'Invalid user identifier' });
      }
    } catch (tokenError) {
      console.error('[Auth] JWT token parsing failed:', tokenError);
      return res.status(401).json({ message: 'Invalid token structure' });
    }

    // Get the user from our database with enhanced validation
    const dbUser = await getUserByAuthId(user.id);
    if (!dbUser) {
      console.error(`[Auth] User not found in database: ${user.id}`);
      return res.status(401).json({ message: 'User account not found' });
    }

    // Validate user object structure
    if (!dbUser.id || !dbUser.username || typeof dbUser.username !== 'string') {
      console.error(`[Auth] Invalid user structure: ${dbUser.id}`);
      return res.status(401).json({ message: 'Invalid user account data' });
    }

    // Check ban/suspension status with enhanced security
    if (dbUser.banned_at) {
      const banDate = new Date(dbUser.banned_at);
      if (isNaN(banDate.getTime())) {
        console.error(`[Auth] Invalid ban date format: ${dbUser.banned_at}`);
        return res.status(403).json({ message: 'Account access restricted' });
      }
      
      return res.status(403).json({
        message: "Account access restricted",
        reason: dbUser.ban_reason || "Account suspended by administrator"
      });
    }

    if (dbUser.suspended_until) {
      const suspensionDate = new Date(dbUser.suspended_until);
      if (isNaN(suspensionDate.getTime())) {
        console.error(`[Auth] Invalid suspension date format: ${dbUser.suspended_until}`);
        return res.status(403).json({ message: 'Account access restricted' });
      }

      if (suspensionDate > new Date()) {
        return res.status(403).json({
          message: "Account temporarily suspended",
          until: dbUser.suspended_until,
          reason: dbUser.ban_reason || "Account suspended temporarily"
        });
      }
    }

    // Set user context for request
    req.user = dbUser;
    
    // Log successful authentication (without sensitive data)
    console.log(`[Auth] User authenticated successfully: ${dbUser.username} (${dbUser.id})`);
    
    next();
  } catch (error) {
    // Secure error handling - log details server-side only
    console.error('[Auth] Authentication verification error:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection?.remoteAddress
    });

    // Generic error response to client
    return res.status(401).json({ message: 'Authentication verification failed' });
  }
}

export function setupAuth(app: Express) {
  const isDev = process.env.NODE_ENV !== "production";

  // CORS configuration for Supabase Auth
  app.use(cors({
    origin: isDev ? 'http://localhost:5173' : true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }));

  // Auth routes for Supabase integration

  // Login with email/password using Supabase Auth
  app.post("/api/login", async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: "Email and password are required" });
      }

      console.log('[Auth] Processing login request for:', email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.log('[Auth] Login failed:', error.message);
        return res.status(401).json({ message: error.message });
      }

      if (!data.user) {
        return res.status(401).json({ message: "Authentication failed" });
      }

      // Get user from our database
      const dbUser = await getUserByAuthId(data.user.id);
      if (!dbUser) {
        return res.status(401).json({ message: "User not found" });
      }

      // Check ban/suspension status
      if (dbUser.banned_at) {
        return res.status(403).json({
          message: "Account banned",
          reason: dbUser.ban_reason
        });
      }

      if (dbUser.suspended_until && new Date(dbUser.suspended_until) > new Date()) {
        return res.status(403).json({
          message: "Account suspended",
          until: dbUser.suspended_until,
          reason: dbUser.ban_reason
        });
      }

      // Update login info
      await supabaseAdmin
        .from('users')
        .update({
          last_login: new Date().toISOString(),
          login_count: dbUser.login_count + 1
        })
        .eq('id', dbUser.id);

      console.log(`[Auth] User logged in successfully: ${dbUser.id}`);
      res.json({
        user: dbUser,
        session: data.session
      });
    } catch (error) {
      console.error('[Auth] Login error:', error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Register with email/password using Supabase Auth
  app.post("/api/register", async (req, res) => {
    try {
      const { email, password, username } = req.body;

      if (!email || !password || !username) {
        return res.status(400).json({ message: "Email, password, and username are required" });
      }

      console.log('[Auth] Processing registration request for:', email);

      // Check if username already exists
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('id')
        .eq('username', username)
        .single();

      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      // Create auth user in Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username
          }
        }
      });

      if (error) {
        console.log('[Auth] Registration failed:', error.message);
        return res.status(400).json({ message: error.message });
      }

      if (!data.user) {
        return res.status(400).json({ message: "Registration failed" });
      }

      // Create user in our database
      const dbUser = await createUserWithAuthId({
        username,
        password: '', // Not needed with Supabase Auth
        is_admin: false,
        bio: '',
        auto_encryption_enabled: true,
        created_at: new Date().toISOString(),
        last_password_change: new Date().toISOString(),
        login_count: 0,
        auth_id: data.user.id
      });

      console.log(`[Auth] User registered successfully: ${dbUser.id}`);
      res.status(201).json({
        user: dbUser,
        session: data.session
      });
    } catch (error) {
      console.error('[Auth] Registration error:', error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Logout using Supabase Auth
  app.post("/api/logout", async (req, res) => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[Auth] Logout error:', error);
        return res.status(500).json({ message: "Logout failed" });
      }
      res.sendStatus(200);
    } catch (error) {
      console.error('[Auth] Logout error:', error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get current user (requires token in Authorization header)
  app.get("/api/user", verifySupabaseToken, (req, res) => {
    res.json(req.user);
  });
}