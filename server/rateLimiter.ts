import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Rate limiting configuration schema
const RateLimitConfigSchema = z.object({
  windowMs: z.number().positive(),
  maxRequests: z.number().positive(),
  skipSuccessfulRequests: z.boolean().default(false),
  skipFailedRequests: z.boolean().default(false),
  keyGenerator: z.function().optional(),
  message: z.string().optional(),
});

type RateLimitConfig = z.infer<typeof RateLimitConfigSchema>;

// Rate limit store interface
interface RateLimitStore {
  get(key: string): Promise<number | null>;
  set(key: string, value: number, ttl: number): Promise<void>;
  reset(key: string): Promise<void>;
}

// Memory store implementation (in production, use Redis or similar)
class MemoryRateLimitStore implements RateLimitStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  async get(key: string): Promise<number | null> {
    const entry = this.store.get(key);
    if (!entry) return null;
    
    // Check if entry has expired
    if (Date.now() > entry.resetTime) {
      this.store.delete(key);
      return null;
    }
    
    return entry.count;
  }

  async set(key: string, value: number, ttl: number): Promise<void> {
    const resetTime = Date.now() + ttl;
    this.store.set(key, { count: value, resetTime });
  }

  async reset(key: string): Promise<void> {
    this.store.delete(key);
  }
}

// Enhanced rate limiter with exponential backoff
class EnhancedRateLimiter {
  private store: RateLimitStore;
  private config: RateLimitConfig;
  private backoffEnabled: boolean;
  private maxBackoffTime: number;

  constructor(config: RateLimitConfig, backoffEnabled = true, maxBackoffTime = 300000) {
    this.config = RateLimitConfigSchema.parse(config);
    this.store = new MemoryRateLimitStore();
    this.backoffEnabled = backoffEnabled;
    this.maxBackoffTime = maxBackoffTime;
  }

  // Generate key for rate limiting (can be overridden)
  private generateKey(req: Request): string {
    const userId = req.user?.id;
    const ip = req.ip || req.connection?.remoteAddress || 'unknown';
    
    // Use user ID if available, otherwise use IP
    const identifier = userId ? `user:${userId}` : `ip:${ip}`;
    
    // Add route path for more granular control
    const route = req.path;
    
    return `${identifier}:${route}`;
  }

  // Calculate backoff time with exponential decay
  private calculateBackoff(attempts: number): number {
    if (!this.backoffEnabled) return 0;
    
    // Exponential backoff: base * (2^attempts)
    const baseDelay = 1000; // 1 second base
    const maxDelay = this.maxBackoffTime;
    const delay = Math.min(baseDelay * Math.pow(2, attempts - 1), maxDelay);
    
    return delay;
  }

  // Check if request should be allowed
  async isAllowed(req: Request): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
    reason?: string;
  }> {
    const key = this.config.keyGenerator 
      ? await this.config.keyGenerator(req) 
      : this.generateKey(req);
    
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    // Get current count
    let currentCount = await this.store.get(key as string);
    if (!currentCount) {
      currentCount = 0;
    }
    
    // Check if window has expired
    const entry = (this.store as any).store.get(key as string);
    if (entry && now > entry.resetTime) {
      await this.store.reset(key as string);
      currentCount = 0;
    }
    
    // Check if request exceeds limit
    if (currentCount >= this.config.maxRequests) {
      // Calculate retry-after with backoff
      const retryAfter = this.calculateBackoff(currentCount);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: now + this.config.windowMs,
        retryAfter: retryAfter > 0 ? Math.ceil(retryAfter / 1000) : undefined,
        reason: 'Rate limit exceeded'
      };
    }
    
    // Increment counter
    await this.store.set(key as string, currentCount + 1, this.config.windowMs);
    
    return {
      allowed: true,
      remaining: this.config.maxRequests - (currentCount + 1),
      resetTime: now + this.config.windowMs
    };
  }

  // Middleware function
  middleware(req: Request, res: Response, next: NextFunction): void {
    this.isAllowed(req).then(result => {
      if (!result.allowed) {
        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': this.config.maxRequests.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
          'Retry-After': result.retryAfter?.toString() || '60'
        });
        
        // Send error response
        const message = this.config.message || 
          `Too many requests. Please try again in ${result.retryAfter || 60} seconds.`;
        
        res.status(429).json({
          error: 'Too Many Requests',
          message,
          retryAfter: result.retryAfter
        });
        return;
      }
      
      // Set rate limit headers for successful requests
      res.set({
        'X-RateLimit-Limit': this.config.maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString()
      });
      
      next();
    }).catch(error => {
      console.error('Rate limiter error:', error);
      next(error);
    });
  }
}

// Pre-configured rate limiters for different endpoint types
export const rateLimiters = {
  // Auth endpoints - strict limits
  auth: new EnhancedRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    message: 'Too many authentication attempts. Please try again later.'
  }),
  
  // API endpoints - moderate limits
  api: new EnhancedRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    message: 'Too many API requests. Please slow down.'
  }),
  
  // File upload endpoints - strict limits
  upload: new EnhancedRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 3,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    message: 'Too many file uploads. Please wait before uploading again.'
  }),
  
  // AI chat endpoints - higher limits but with backoff
  aiChat: new EnhancedRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    message: 'AI chat rate limit exceeded. Please wait before sending more messages.'
  }),
  
  // Admin endpoints - very strict limits
  admin: new EnhancedRateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    message: 'Admin API rate limit exceeded.'
  })
};

// Request validator middleware
export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Validation failed',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
      }
      next(error);
    }
  };
};

// Security headers middleware
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Security headers
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' data:; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'",
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
  });
  
  next();
};

// IP-based rate limiting with geographic awareness
export const ipRateLimiter = new EnhancedRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100,
  skipSuccessfulRequests: false,
  skipFailedRequests: false,
  keyGenerator: async (req: any) => {
    const ip = req.ip || req.connection?.remoteAddress || 'unknown';
    // Add geographic information if available
    // In production, you could use a geolocation service here
    return `ip:${ip}`;
  }
});

// Export middleware functions
export { EnhancedRateLimiter };