import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import DOMPurify from 'dompurify';

// Content validation schemas
export const contentValidationSchemas = {
  thread: z.object({
    title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
    content: z.string().min(1, 'Content is required').max(10000, 'Content too long'),
    category: z.string().optional(),
    tags: z.array(z.string()).optional().default([])
  }),
  
  reply: z.object({
    content: z.string().min(1, 'Content is required').max(5000, 'Content too long')
  }),
  
  message: z.object({
    recipientId: z.number().positive('Invalid recipient ID'),
    content: z.string().min(1, 'Message content is required').max(1000, 'Message too long'),
    nonce: z.string().optional(),
    isEncrypted: z.boolean().default(false)
  }),
  
  bio: z.object({
    bio: z.string().max(500, 'Bio too long').optional()
  }),
  
  news: z.object({
    title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
    content: z.string().min(1, 'Content is required').max(10000, 'Content too long'),
    excerpt: z.string().max(500, 'Excerpt too long').optional(),
    isPublished: z.boolean().default(false)
  })
};

// Enhanced content filtering with multiple layers
export class ContentFilter {
  private offensiveWords: Set<string>;
  private suspiciousPatterns: RegExp[];
  private maxLinks: number;
  private maxMentions: number;

  constructor() {
    this.offensiveWords = new Set([
      'spam', 'scam', 'phishing', 'malware', 'virus', 'hack', 'crack',
      'illegal', 'fraud', 'cheat', 'exploit', 'vulnerable', 'attack',
      'sql injection', 'xss', 'csrf', 'ddos', 'brute force', 'password reset',
      'verify account', 'urgent', 'limited time', 'click here', 'free money',
      'lottery', 'winner', 'congratulations', 'claim prize', 'urgent action required'
    ]);

    this.suspiciousPatterns = [
      /\bhttps?:\/\/[^\s]+/gi, // URLs
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // Credit card numbers
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN patterns
      /\$\d+/g, // Money amounts
      /<script[^>]*>.*?<\/script>/gi, // Script tags
      /javascript:/gi, // JavaScript protocols
      /on\w+\s*=/gi, // Event handlers
      /data:text\/html/gi, // Data URLs
      /eval\s*\(/gi, // eval() calls
      /document\.cookie/gi, // Cookie access
      /window\.location/gi // Location access
    ];

    this.maxLinks = 3;
    this.maxMentions = 10;
  }

  // Sanitize HTML content
  sanitizeHTML(content: string): string {
    return DOMPurify.sanitize(content, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'blockquote', 'code', 'pre'],
      ALLOWED_ATTR: ['class', 'style'],
      FORBID_ATTR: ['id', 'onclick', 'onload', 'onerror', 'javascript:', 'data:']
    });
  }

  // Check for offensive language
  containsOffensiveLanguage(content: string): boolean {
    const lowerContent = content.toLowerCase();
    return Array.from(this.offensiveWords).some(word => lowerContent.includes(word));
  }

  // Check for suspicious patterns
  containsSuspiciousPatterns(content: string): { found: boolean; patterns: string[] } {
    const foundPatterns: string[] = [];
    
    this.suspiciousPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        foundPatterns.push(...matches);
      }
    });

    return {
      found: foundPatterns.length > 0,
      patterns: foundPatterns
    };
  }

  // Count links and mentions
  analyzeContentStructure(content: string): {
    linkCount: number;
    mentionCount: number;
    hasExcessiveLinks: boolean;
    hasExcessiveMentions: boolean;
  } {
    const linkMatches = content.match(/\bhttps?:\/\/[^\s]+/gi) || [];
    const mentionMatches = content.match(/@\w+/g) || [];

    return {
      linkCount: linkMatches.length,
      mentionCount: mentionMatches.length,
      hasExcessiveLinks: linkMatches.length > this.maxLinks,
      hasExcessiveMentions: mentionMatches.length > this.maxMentions
    };
  }

  // Comprehensive content analysis
  analyzeContent(content: string): {
    isValid: boolean;
    issues: string[];
    sanitizedContent?: string;
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const issues: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';

    // Check for offensive language
    if (this.containsOffensiveLanguage(content)) {
      issues.push('Content contains potentially offensive language');
      riskLevel = 'high';
    }

    // Check for suspicious patterns
    const suspiciousResult = this.containsSuspiciousPatterns(content);
    if (suspiciousResult.found) {
      issues.push(`Content contains suspicious patterns: ${suspiciousResult.patterns.slice(0, 3).join(', ')}`);
      riskLevel = 'high';
    }

    // Analyze content structure
    const structure = this.analyzeContentStructure(content);
    if (structure.hasExcessiveLinks) {
      issues.push(`Excessive number of links (${structure.linkCount} > ${this.maxLinks})`);
      riskLevel = riskLevel === 'high' ? 'high' : 'medium';
    }

    if (structure.hasExcessiveMentions) {
      issues.push(`Excessive number of mentions (${structure.mentionCount} > ${this.maxMentions})`);
      riskLevel = riskLevel === 'high' ? 'high' : 'medium';
    }

    // Sanitize HTML content
    const sanitizedContent = this.sanitizeHTML(content);

    return {
      isValid: issues.length === 0,
      issues,
      sanitizedContent,
      riskLevel
    };
  }
}

// Create global content filter instance
export const contentFilter = new ContentFilter();

// Input validation middleware
export const validateInput = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }));
        
        return res.status(400).json({
          error: 'Validation failed',
          issues,
          timestamp: new Date().toISOString()
        });
      }
      
      next(error);
    }
  };
};

// Content filtering middleware
export const filterContent = (field: string = 'content') => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const content = req.body[field];
      
      if (typeof content !== 'string') {
        return res.status(400).json({
          error: 'Invalid content format',
          timestamp: new Date().toISOString()
        });
      }

      const analysis = contentFilter.analyzeContent(content);
      
      // Log content analysis for monitoring
      console.log(`Content analysis for ${field}:`, {
        riskLevel: analysis.riskLevel,
        issuesCount: analysis.issues.length,
        contentLength: content.length,
        timestamp: new Date().toISOString()
      });

      if (!analysis.isValid) {
        // For high-risk content, reject it
        if (analysis.riskLevel === 'high') {
          return res.status(400).json({
            error: 'Content validation failed',
            message: 'Content contains potentially harmful material',
            issues: analysis.issues,
            riskLevel: analysis.riskLevel,
            timestamp: new Date().toISOString()
          });
        }
        
        // For medium-risk content, warn but allow (with sanitized content)
        console.warn(`Medium-risk content detected:`, {
          issues: analysis.issues,
          userAgent: req.headers['user-agent'],
          ip: req.ip
        });
      }

      // Replace content with sanitized version
      req.body[field] = analysis.sanitizedContent || content;
      next();
    } catch (error) {
      console.error('Content filtering error:', error);
      next(error);
    }
  };
};

// Request size limiter
export const limitRequestSize = (maxSizeInBytes: number = 1024 * 1024) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.headers['content-length'];
    
    if (contentLength) {
      const bytes = parseInt(contentLength, 10);
      if (bytes > maxSizeInBytes) {
        return res.status(413).json({
          error: 'Request entity too large',
          maxSize: maxSizeInBytes,
          actualSize: bytes,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    next();
  };
};

// SQL injection protection middleware
export const preventSQLInjection = (req: Request, res: Response, next: NextFunction) => {
  const suspiciousPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|JOIN|WHERE|HAVING|GROUP BY|ORDER BY)\b)/gi,
    /('|--|\/\*|\*\/|;|\||\x00)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi
  ];

  const checkValue = (value: any): boolean => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj: any): boolean => {
    for (const key in obj) {
      if (checkValue(obj[key])) {
        return true;
      }
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        if (checkObject(obj[key])) {
          return true;
        }
      }
    }
    return false;
  };

  if (checkObject(req.body) || checkObject(req.params) || checkObject(req.query)) {
    console.warn('Potential SQL injection attempt detected:', {
      body: req.body,
      params: req.params,
      query: req.query,
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      timestamp: new Date().toISOString()
    });

    return res.status(400).json({
      error: 'Invalid input format',
      message: 'Request contains potentially malicious content',
      timestamp: new Date().toISOString()
    });
  }

  next();
};

// XSS protection middleware
export const preventXSS = (req: Request, res: Response, next: NextFunction) => {
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /document\.cookie/gi,
    /window\.location/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>.*?<\/embed>/gi,
    /<link[^>]*rel\s*=\s*['"]?stylesheet['"]?[^>]*>/gi
  ];

  const checkValue = (value: any): boolean => {
    if (typeof value === 'string') {
      return xssPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const checkObject = (obj: any): boolean => {
    for (const key in obj) {
      if (checkValue(obj[key])) {
        return true;
      }
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        if (checkObject(obj[key])) {
          return true;
        }
      }
    }
    return false;
  };

  if (checkObject(req.body) || checkObject(req.params) || checkObject(req.query)) {
    console.warn('Potential XSS attack detected:', {
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      timestamp: new Date().toISOString()
    });

    return res.status(400).json({
      error: 'Invalid input format',
      message: 'Request contains potentially malicious content',
      timestamp: new Date().toISOString()
    });
  }

  next();
};

// Comprehensive security middleware
export const securityMiddleware = [
  limitRequestSize(1024 * 1024), // 1MB limit
  preventSQLInjection,
  preventXSS
];
