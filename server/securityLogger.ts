import { Request, Response, NextFunction } from 'express';
// Simple logger implementation to avoid winston dependency
const createLogger = () => ({
  log: (level: string, message: string, meta?: any) => {
    console[level](`[${level.toUpperCase()}] ${message}`, meta || '');
  }
});

const format = {
  combine: (...args: any[]) => ({}),
  timestamp: () => ({}),
  json: () => ({}),
  errors: (opts: any) => ({}),
  colorize: () => ({}),
  simple: () => ({})
};

const transports = {
  File: (opts: any) => ({}),
  Console: (opts: any) => ({})
};
import { z } from 'zod';

// Security event schema
const securityEventSchema = z.object({
  timestamp: z.string(),
  level: z.enum(['info', 'warn', 'error', 'debug']),
  event: z.string(),
  category: z.enum(['auth', 'rate_limit', 'content_filter', 'xss', 'sql_injection', 'file_upload', 'admin']),
  userId: z.number().optional(),
  ip: z.string(),
  userAgent: z.string(),
  method: z.string(),
  url: z.string(),
  details: z.record(z.any()).optional(),
  severity: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  sessionId: z.string().optional()
});

type SecurityEvent = z.infer<typeof securityEventSchema>;

// Security logger configuration
const securityLogger = {
  log: (level: string, message: string, meta?: any) => {
    const logMethod = (console as any)[level] || console.log;
    logMethod(`[${level.toUpperCase()}] ${message}`, meta || '');
  },
  error: (message: string, meta?: any) => {
    console.error(`[ERROR] ${message}`, meta || '');
  }
};

// Security monitoring service
export class SecurityMonitor {
  private securityEvents: SecurityEvent[] = [];
  private maxEvents: number = 1000;
  private alertThresholds: {
    failedAuth: number;
    rateLimit: number;
    contentFilter: number;
    suspiciousActivity: number;
  };

  constructor() {
    this.alertThresholds = {
      failedAuth: 5, // 5 failed auth attempts in 5 minutes
      rateLimit: 10, // 10 rate limit hits in 1 minute
      contentFilter: 5, // 5 content filter hits in 5 minutes
      suspiciousActivity: 3 // 3 suspicious activities in 10 minutes
    };
  }

  // Log security event
  logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>) {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date().toISOString()
    };

    // Validate event structure
    const validationResult = securityEventSchema.safeParse(securityEvent);
    if (!validationResult.success) {
      console.error('Invalid security event structure:', validationResult.error);
      return;
    }

    // Add to in-memory storage
    this.securityEvents.push(securityEvent);

    // Keep only recent events
    if (this.securityEvents.length > this.maxEvents) {
      this.securityEvents = this.securityEvents.slice(-this.maxEvents);
    }

    // Log to file
    securityLogger.log(securityEvent.level, securityEvent.event, {
      category: securityEvent.category,
      userId: securityEvent.userId,
      ip: securityEvent.ip,
      userAgent: securityEvent.userAgent,
      method: securityEvent.method,
      url: securityEvent.url,
      details: securityEvent.details,
      severity: securityEvent.severity
    });

    // Check for alerts
    this.checkForAlerts(securityEvent);
  }

  // Check for security alerts
  private checkForAlerts(event: SecurityEvent) {
    const recentEvents = this.getRecentEvents(5 * 60 * 1000); // 5 minutes

    switch (event.category) {
      case 'auth':
        this.checkAuthAlerts(recentEvents);
        break;
      case 'rate_limit':
        this.checkRateLimitAlerts(recentEvents);
        break;
      case 'content_filter':
        this.checkContentFilterAlerts(recentEvents);
        break;
      default:
        this.checkSuspiciousActivityAlerts(recentEvents);
    }
  }

  // Check authentication alerts
  private checkAuthAlerts(events: SecurityEvent[]) {
    const failedAuths = events.filter(e => 
      e.category === 'auth' && 
      e.event.includes('failed') && 
      e.userId === undefined
    );

    if (failedAuths.length >= this.alertThresholds.failedAuth) {
      this.triggerAlert('authentication_brute_force', {
        failedAttempts: failedAuths.length,
        timeWindow: '5 minutes',
        recentEvents: failedAuths.slice(-5)
      });
    }
  }

  // Check rate limit alerts
  private checkRateLimitAlerts(events: SecurityEvent[]) {
    const rateLimitEvents = events.filter(e => e.category === 'rate_limit');
    
    if (rateLimitEvents.length >= this.alertThresholds.rateLimit) {
      this.triggerAlert('rate_limit_exceeded', {
        eventsCount: rateLimitEvents.length,
        timeWindow: '1 minute',
        topIPs: this.getTopIPs(rateLimitEvents, 3)
      });
    }
  }

  // Check content filter alerts
  private checkContentFilterAlerts(events: SecurityEvent[]) {
    const filterEvents = events.filter(e => e.category === 'content_filter');
    
    if (filterEvents.length >= this.alertThresholds.contentFilter) {
      this.triggerAlert('content_filter_violations', {
        violations: filterEvents.length,
        timeWindow: '5 minutes',
        topIPs: this.getTopIPs(filterEvents, 3)
      });
    }
  }

  // Check suspicious activity alerts
  private checkSuspiciousActivityAlerts(events: SecurityEvent[]) {
    const suspiciousEvents = events.filter(e => 
      e.severity === 'high' || e.severity === 'critical'
    );

    if (suspiciousEvents.length >= this.alertThresholds.suspiciousActivity) {
      this.triggerAlert('suspicious_activity_detected', {
        suspiciousEvents: suspiciousEvents.length,
        timeWindow: '10 minutes',
        events: suspiciousEvents.slice(-3)
      });
    }
  }

  // Trigger security alert
  private triggerAlert(type: string, details: any) {
    const alert = {
      timestamp: new Date().toISOString(),
      type,
      severity: 'high',
      details,
      triggeredBy: 'security_monitor'
    };

    // Log alert
    (securityLogger as any).error('Security alert triggered', alert);

    // In production, you could integrate with external services:
    // - Send to Slack/Teams
    // - Send to email
    // - Send to SIEM system
    // - Send to security monitoring service

    console.warn('🚨 SECURITY ALERT:', alert);
  }

  // Get recent events within time window
  private getRecentEvents(timeWindowMs: number): SecurityEvent[] {
    const cutoff = Date.now() - timeWindowMs;
    return this.securityEvents.filter(e => 
      new Date(e.timestamp).getTime() > cutoff
    );
  }

  // Get top IPs by event count
  private getTopIPs(events: SecurityEvent[], limit: number): Array<{ ip: string; count: number }> {
    const ipCounts = events.reduce((acc, event) => {
      acc[event.ip] = (acc[event.ip] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(ipCounts)
      .map(([ip, count]) => ({ ip, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  // Get security statistics
  getSecurityStats() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneDay = 24 * 60 * 60 * 1000;

    const recentHour = this.getRecentEvents(oneHour);
    const recentDay = this.getRecentEvents(oneDay);

    return {
      totalEvents: this.securityEvents.length,
      recentHour: {
        total: recentHour.length,
        byCategory: this.groupByCategory(recentHour),
        bySeverity: this.groupBySeverity(recentHour),
        topIPs: this.getTopIPs(recentHour, 5)
      },
      recentDay: {
        total: recentDay.length,
        byCategory: this.groupByCategory(recentDay),
        bySeverity: this.groupBySeverity(recentDay),
        topIPs: this.getTopIPs(recentDay, 10)
      },
      alerts: this.getRecentAlerts(24 * 60 * 60 * 1000) // Last 24 hours
    };
  }

  // Group events by category
  private groupByCategory(events: SecurityEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      acc[event.category] = (acc[event.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  // Group events by severity
  private groupBySeverity(events: SecurityEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      acc[event.severity] = (acc[event.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  // Get recent alerts (mock implementation)
  private getRecentAlerts(timeWindowMs: number): any[] {
    // In a real implementation, this would query your alert system
    return [];
  }

  // Export security events
  exportEvents(startDate?: Date, endDate?: Date): SecurityEvent[] {
    let events = [...this.securityEvents];

    if (startDate) {
      events = events.filter(e => new Date(e.timestamp) >= startDate);
    }

    if (endDate) {
      events = events.filter(e => new Date(e.timestamp) <= endDate);
    }

    return events;
  }

  // Clear old events
  clearOldEvents(keepDays: number = 30) {
    const cutoff = Date.now() - (keepDays * 24 * 60 * 60 * 1000);
    this.securityEvents = this.securityEvents.filter(e => 
      new Date(e.timestamp).getTime() > cutoff
    );
  }
}

// Create global security monitor instance
export const securityMonitor = new SecurityMonitor();

// Security logging middleware
export const securityLoggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

  // Add request ID to response headers
  res.set('X-Request-ID', requestId);

  // Log request
  securityMonitor.logSecurityEvent({
    level: 'info',
    event: 'request_received',
    category: 'auth' as const,
    ip: req.ip || req.connection?.remoteAddress || 'unknown',
    userAgent: req.headers['user-agent'] || 'unknown',
    method: req.method,
    url: req.url,
    details: {
      requestId,
      contentLength: req.headers['content-length'],
      contentType: req.headers['content-type']
    },
    severity: 'low'
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const responseTime = Date.now() - startTime;
    
    // Log response
    securityMonitor.logSecurityEvent({
      level: res.statusCode >= 400 ? 'warn' : 'info',
      event: 'response_sent',
      category: 'auth' as const,
      ip: req.ip || req.connection?.remoteAddress || 'unknown',
      userAgent: req.headers['user-agent'] || 'unknown',
      method: req.method,
      url: req.url,
      details: {
        requestId,
        statusCode: res.statusCode,
        responseTime,
        contentLength: res.get('content-length')
      },
      severity: res.statusCode >= 400 ? 'medium' : 'low'
    });

    // Call original end
    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

// Security event logging helpers
export const logSecurityEvent = (event: Omit<SecurityEvent, 'timestamp'>) => {
  securityMonitor.logSecurityEvent(event);
};

export const logFailedAuth = (ip: string, userAgent: string, reason: string) => {
  securityMonitor.logSecurityEvent({
    level: 'warn',
    event: 'failed_authentication',
    category: 'auth',
    ip,
    userAgent,
    method: 'POST',
    url: '/api/login',
    details: { reason },
    severity: 'medium'
  });
};

export const logRateLimitExceeded = (ip: string, userAgent: string, endpoint: string) => {
  securityMonitor.logSecurityEvent({
    level: 'warn',
    event: 'rate_limit_exceeded',
    category: 'rate_limit',
    ip,
    userAgent,
    method: 'GET',
    url: endpoint,
    severity: 'medium'
  });
};

export const logContentFilterViolation = (ip: string, userAgent: string, content: string, violations: string[]) => {
  securityMonitor.logSecurityEvent({
    level: 'warn',
    event: 'content_filter_violation',
    category: 'content_filter',
    ip,
    userAgent,
    method: 'POST',
    url: '/api/threads', // Could be any endpoint
    details: { 
      contentPreview: content.substring(0, 100),
      violations 
    },
    severity: 'medium'
  });
};

export const logSuspiciousActivity = (ip: string, userAgent: string, activity: string, severity: 'high' | 'critical' = 'high') => {
  securityMonitor.logSecurityEvent({
    level: 'error',
    event: 'suspicious_activity_detected',
    category: 'auth' as const,
    ip,
    userAgent,
    method: 'GET',
    url: '/api/admin', // Could be any admin endpoint
    details: { activity },
    severity
  });
};

// Security health check endpoint
export const securityHealthCheck = (req: Request, res: Response) => {
  const stats = securityMonitor.getSecurityStats();
  
  res.json({
    success: true,
    security: {
      status: 'active',
      monitoring: 'enabled',
      totalEvents: stats.totalEvents,
      recentHour: {
        total: stats.recentHour.total,
        topCategories: Object.entries(stats.recentHour.byCategory)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5),
        topIPs: stats.recentHour.topIPs.slice(0, 5)
      },
      alerts: stats.alerts.length,
      timestamp: new Date().toISOString()
    }
  });
};