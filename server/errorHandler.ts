import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Error types
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = details;
    
    // Ensure the prototype chain is correct
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, details);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {
    super(message, 429, true, { retryAfter });
  }
}

export class ContentFilterError extends AppError {
  constructor(message: string = 'Content validation failed') {
    super(message, 422, true);
  }
}

// Standardized error response format
export const createErrorResponse = (
  error: Error | AppError,
  req: Request,
  includeStackTrace: boolean = false
) => {
  const response = {
    success: false,
    error: {
      message: error.message,
      code: error instanceof AppError ? error.statusCode : 500,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method,
      requestId: req.headers['x-request-id'] || 'unknown'
    }
  };

  // Add operational details for AppError instances
  if (error instanceof AppError && error.details) {
    (response.error as any).details = error.details;
  }

  // Add validation error details for Zod errors
  if (error instanceof z.ZodError) {
    (response.error as any).validation = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }));
  }

  // Include stack trace in development
  if (includeStackTrace && process.env.NODE_ENV === 'development') {
    (response.error as any).stack = error.stack;
  }

  // Log error details
  console.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    timestamp: new Date().toISOString(),
    isOperational: error instanceof AppError ? error.isOperational : false
  });

  return response;
};

// Error handling middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Create standardized error response
  const errorResponse = createErrorResponse(error, req, isDevelopment);
  
  // Set appropriate status code
  const statusCode = error instanceof AppError ? error.statusCode : 500;
  
  // Send error response
  res.status(statusCode).json(errorResponse);
};

// Async error wrapper for route handlers
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler for unmatched routes
export const notFoundHandler = (req: Request, res: Response) => {
  const errorResponse = createErrorResponse(
    new NotFoundError(`Route ${req.originalUrl} not found`),
    req
  );
  
  res.status(404).json(errorResponse);
};

// Global error handler configuration
export const errorHandlingConfig = {
  // Enable error logging
  enableLogging: true,
  
  // Enable error tracking (could integrate with services like Sentry)
  enableTracking: false,
  
  // Maximum error details to include in response
  maxErrorDetails: 1000,
  
  // Custom error formatters
  formatters: {
    validation: (error: z.ZodError) => ({
      type: 'validation',
      errors: error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      }))
    }),
    
    database: (error: any) => ({
      type: 'database',
      code: error.code,
      table: error.table,
      constraint: error.constraint,
      detail: error.detail
    }),
    
    authentication: (error: Error) => ({
      type: 'authentication',
      challenge: 'basic' // Could be 'basic', 'bearer', etc.
    })
  }
};

// Request validation middleware with error handling
export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationError = new ValidationError('Validation failed', {
          fields: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        });
        
        const errorResponse = createErrorResponse(validationError, req);
        return res.status(400).json(errorResponse);
      }
      
      next(error);
    }
  };
};

// Error monitoring and logging
export class ErrorMonitor {
  private errors: Array<{
    timestamp: Date;
    error: Error;
    request: {
      method: string;
      url: string;
      ip: string;
      userAgent: string;
    };
  }> = [];

  constructor(private maxErrors: number = 1000) {}

  logError(error: Error, req: Request) {
    const errorEntry = {
      timestamp: new Date(),
      error,
      request: {
        method: req.method,
        url: req.url,
        ip: req.ip || req.connection?.remoteAddress,
        userAgent: req.headers['user-agent']
      }
    };

    this.errors.push(errorEntry as any);

    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // Log to console
    console.error('Error logged:', {
      timestamp: errorEntry.timestamp,
      error: error.message,
      url: errorEntry.request.url,
      ip: errorEntry.request.ip
    });
  }

  getRecentErrors(count: number = 50) {
    return this.errors.slice(-count);
  }

  getErrorStats() {
    const total = this.errors.length;
    const byType = this.errors.reduce((acc, entry) => {
      const type = entry.error.constructor.name;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      byType,
      recent: this.getRecentErrors(10)
    };
  }
}

// Global error monitor instance
export const globalErrorMonitor = new ErrorMonitor();

// Enhanced error handling with monitoring
export const monitoredErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log error to monitor
  globalErrorMonitor.logError(error, req);
  
  // Use standard error handler
  errorHandler(error, req, res, next);
};

// Health check endpoint for error monitoring
export const errorMonitoringHealthCheck = (req: Request, res: Response) => {
  const stats = globalErrorMonitor.getErrorStats();
  
  res.json({
    success: true,
    errorMonitoring: {
      status: 'active',
      totalErrors: stats.total,
      errorTypes: stats.byType,
      recentErrors: stats.recent.length,
      timestamp: new Date().toISOString()
    }
  });
};