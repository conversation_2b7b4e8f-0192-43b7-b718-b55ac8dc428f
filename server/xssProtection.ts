import { Request, Response, NextFunction } from 'express';
import DOMPurify from 'dompurify';

// XSS protection configuration
export const xssProtectionConfig = {
  // Allowed HTML tags for different content types
  allowedTags: {
    // Minimal safe tags for user-generated content
    minimal: ['p', 'br', 'strong', 'em', 'u', 'blockquote'],
    // Extended tags for admin/content
    extended: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'blockquote', 'code', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    // Rich content for trusted users
    rich: ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'blockquote', 'code', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'a', 'table', 'thead', 'tbody', 'tr', 'th', 'td']
  },
  
  // Allowed attributes
  allowedAttributes: {
    global: ['class', 'style'],
    tags: {
      a: ['href', 'title', 'target'],
      img: ['src', 'alt', 'title', 'width', 'height'],
      code: ['class'],
      pre: ['class']
    }
  },
  
  // Forbidden attributes
  forbiddenAttributes: [
    'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
    'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
    'javascript:', 'data:', 'vbscript:', 'eval'
  ],
  
  // URL schemes to allow
  allowedUrlSchemes: ['http', 'https', 'mailto', 'tel', 'ftp'],
  
  // Maximum content length
  maxContentLength: 100000, // 100KB
};

// XSS protection class
export class XSSProtection {
  private config: typeof xssProtectionConfig;

  constructor(config: Partial<typeof xssProtectionConfig> = {}) {
    this.config = { ...xssProtectionConfig, ...config };
  }

  // Sanitize HTML content based on content type
  sanitizeHTML(content: string, contentType: 'minimal' | 'extended' | 'rich' = 'minimal'): string {
    if (typeof content !== 'string') {
      return '';
    }

    // Check content length
    if (content.length > this.config.maxContentLength) {
      throw new Error(`Content exceeds maximum length of ${this.config.maxContentLength} characters`);
    }

    // Get allowed tags for content type
    const allowedTags = this.config.allowedTags[contentType] || this.config.allowedTags.minimal;

    // Configure DOMPurify
    const purified = DOMPurify.sanitize(content, {
      ALLOWED_TAGS: allowedTags,
      ALLOWED_ATTR: this.config.allowedAttributes.global,
      FORBID_ATTR: this.config.forbiddenAttributes,
      ALLOWED_URI_REGEXP: this.getAllowedUriRegex(),
      USE_PROFILES: false,
      FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed', 'link', 'meta', 'title', 'head']
    });

    return purified;
  }

  // Get allowed URI regex
  private getAllowedUriRegex(): RegExp {
    const allowedSchemes = this.config.allowedUrlSchemes.join('|');
    return new RegExp(`^(${allowedSchemes}):`, 'i');
  }

  // Check for XSS patterns in plain text
  containsXSSPatterns(content: string): boolean {
    const xssPatterns = [
      // Script tags
      /<script[^>]*>.*?<\/script>/gi,
      /<script[^>]*>/gi,
      /<\/script>/gi,
      
      // Event handlers
      /on\w+\s*=/gi,
      /on\w+\s*:\s*function/gi,
      
      // JavaScript protocols
      /javascript:/gi,
      /vbscript:/gi,
      
      // Data URLs
      /data:text\/html/gi,
      /data:application\/x-javascript/gi,
      
      // Dangerous attributes
      /src\s*=\s*['"]?javascript:/gi,
      /href\s*=\s*['"]?javascript:/gi,
      
      // eval() and similar
      /eval\s*\(/gi,
      /setTimeout\s*\(/gi,
      /setInterval\s*\(/gi,
      
      // Document access
      /document\.cookie/gi,
      /document\.write/gi,
      /window\.location/gi,
      
      // Iframes and embeds
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi,
      /<embed[^>]*>.*?<\/embed>/gi,
      
      // CSS expressions
      /expression\s*\(/gi,
      /-moz-binding\s*:\s*url\(.*?\)/gi,
      
      // Base64 encoded content
      /data:text\/html;base64,/gi,
      /data:application\/x-javascript;base64,/gi
    ];

    return xssPatterns.some(pattern => pattern.test(content));
  }

  // Sanitize JSON data
  sanitizeJSON(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeJSON(item));
    }

    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(data)) {
      // Sanitize keys
      const safeKey = this.sanitizeString(key);
      
      // Sanitize values
      if (typeof value === 'string') {
        sanitized[safeKey] = this.sanitizeString(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[safeKey] = this.sanitizeJSON(value);
      } else {
        sanitized[safeKey] = value;
      }
    }

    return sanitized;
  }

  // Sanitize string content
  private sanitizeString(str: string): string {
    // Remove potentially dangerous characters
    return str
      .replace(/[<>]/g, '') // Remove < and >
      .replace(/['"]/g, '') // Remove quotes
      .replace(/\\/g, ''); // Remove backslashes
  }

  // Validate and sanitize user input
  validateInput(input: any, type: 'text' | 'html' | 'json' = 'text'): any {
    if (input === null || input === undefined) {
      return input;
    }

    switch (type) {
      case 'text':
        if (typeof input === 'string') {
          if (this.containsXSSPatterns(input)) {
            throw new Error('Input contains potentially malicious content');
          }
          return this.sanitizeString(input);
        }
        return input;
        
      case 'html':
        if (typeof input === 'string') {
          return this.sanitizeHTML(input);
        }
        return input;
        
      case 'json':
        return this.sanitizeJSON(input);
        
      default:
        return input;
    }
  }
}

// Create global XSS protection instance
export const xssProtection = new XSSProtection();

// XSS protection middleware
export const xssProtectionMiddleware = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Sanitize request body
    if (req.body) {
      req.body = xssProtection.validateInput(req.body, 'json');
    }

    // Sanitize query parameters
    if (req.query) {
      req.query = xssProtection.validateInput(req.query, 'json');
    }

    // Sanitize path parameters
    if (req.params) {
      req.params = xssProtection.validateInput(req.params, 'json');
    }

    next();
  } catch (error) {
    console.error('XSS protection error:', error);
    res.status(400).json({
      error: 'Invalid input',
      message: 'Request contains potentially malicious content',
      timestamp: new Date().toISOString()
    });
  }
};

// Content type-specific XSS protection
export const htmlContentProtection = (req: Request, res: Response, next: NextFunction) => {
  try {
    const contentType = req.headers['content-type'] || '';
    
    if (contentType.includes('text/html') || contentType.includes('application/xhtml+xml')) {
      // Sanitize HTML content
      if (req.body && typeof req.body === 'string') {
        req.body = xssProtection.sanitizeHTML(req.body, 'extended');
      }
    }

    next();
  } catch (error) {
    console.error('HTML content protection error:', error);
    res.status(400).json({
      error: 'Invalid content',
      message: 'HTML content contains potentially malicious elements',
      timestamp: new Date().toISOString()
    });
  }
};

// Rich text protection for admin users
export const richTextProtection = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Only apply to users with appropriate permissions
    const isAdmin = req.user && (req.user as any).isAdmin;
    const contentType = req.headers['content-type'] || '';

    if (isAdmin && (contentType.includes('text/html') || contentType.includes('application/xhtml+xml'))) {
      if (req.body && typeof req.body === 'string') {
        req.body = xssProtection.sanitizeHTML(req.body, 'rich');
      }
    }

    next();
  } catch (error) {
    console.error('Rich text protection error:', error);
    res.status(400).json({
      error: 'Invalid content',
      message: 'Rich text content contains potentially malicious elements',
      timestamp: new Date().toISOString()
    });
  }
};

// Security headers for XSS protection
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  res.set({
    'X-XSS-Protection': '1; mode=block',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' data:; object-src 'none'; base-uri 'self'; frame-ancestors 'none'; form-action 'self'",
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
  });

  next();
};

// Comprehensive XSS protection middleware stack
export const xssProtectionStack = [
  securityHeaders,
  xssProtectionMiddleware,
  htmlContentProtection
];

// Content validation middleware
export const validateContent = (field: string, type: 'text' | 'html' | 'json' = 'text') => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const content = req.body[field];
      
      if (content !== undefined && content !== null) {
        const sanitized = xssProtection.validateInput(content, type);
        
        if (sanitized !== content) {
          console.warn(`Content sanitized for field: ${field}`, {
            originalLength: typeof content === 'string' ? content.length : 0,
            userAgent: req.headers['user-agent'],
            ip: req.ip
          });
        }
        
        req.body[field] = sanitized;
      }
      
      next();
    } catch (error) {
      console.error(`Content validation error for field ${field}:`, error);
      res.status(400).json({
        error: 'Content validation failed',
        field,
        message: error instanceof Error ? error.message : 'Invalid content',
        timestamp: new Date().toISOString()
      });
    }
  };
};
