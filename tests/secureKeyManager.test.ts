import SecureKeyManager from '../client/src/lib/secureKeyManager';

// Mock dependencies
jest.mock('tweetnacl', () => ({
  randomBytes: jest.fn((length: number) => new Uint8Array(Array.from({ length }, () => Math.floor(Math.random() * 256)))),
  box: {
    keyPair: jest.fn(() => ({
      publicKey: new Uint8Array([1, 2, 3, 4, 5]),
      secretKey: new Uint8Array([6, 7, 8, 9, 10]),
    })),
    keyPairFromSecretKey: jest.fn((secretKey: Uint8Array) => ({
      publicKey: new Uint8Array([1, 2, 3, 4, 5]),
      secretKey: secretKey,
    })),
  },
}));

jest.mock('tweetnacl-util', () => ({
  encodeBase64: jest.fn((array: Uint8Array) => Buffer.from(array).toString('base64')),
  decodeBase64: jest.fn((str: string) => new Uint8Array(Buffer.from(str, 'base64'))),
}));

jest.mock('../client/src/lib/logger', () => ({
  clientLogger: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
  },
}));

describe('SecureKeyManager Security Tests', () => {
  let secureKeyManager: SecureKeyManager;

  beforeEach(() => {
    secureKeyManager = SecureKeyManager.getInstance();
    jest.clearAllMocks();
  });

  describe('Security Context Validation', () => {
    test('should validate secure context', () => {
      // Test with secure context
      (global as any).window.isSecureContext = true;
      
      // This should not throw during initialization
      expect(() => {
        SecureKeyManager.getInstance();
      }).not.toThrow();
    });

    test('should warn about insecure context', () => {
      // Test with insecure context
      (global as any).window.isSecureContext = false;
      
      const warnSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      expect(() => {
        SecureKeyManager.getInstance();
      }).not.toThrow();
      
      expect(warnSpy).toHaveBeenCalledWith('[SecureKeyManager Warning] Not running in secure context - potential security risk');
      
      warnSpy.mockRestore();
    });

    test('should detect suspicious global properties', () => {
      // Mock suspicious global property
      (global as any).__defineGetter__ = jest.fn();
      
      const warnSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      expect(() => {
        SecureKeyManager.getInstance();
      }).not.toThrow();
      
      expect(warnSpy).toHaveBeenCalledWith('[SecureKeyManager Warning] Suspicious global property detected: __defineGetter__');
      
      warnSpy.mockRestore();
    });
  });

  describe('Password Validation', () => {
    test('should reject short passwords', async () => {
      await expect(
        secureKeyManager.initializeWithPassword('short')
      ).rejects.toThrow('Password must be at least 8 characters long');
    });

    test('should accept valid passwords', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const result = await secureKeyManager.initializeWithPassword('validpassword123');
      
      expect(result).toHaveProperty('publicKey');
      expect(result).toHaveProperty('keyId');
      expect(typeof result.publicKey).toBe('string');
      expect(typeof result.keyId).toBe('string');
    });
  });

  describe('Key Derivation Security', () => {
    test('should generate secure salt with enhanced entropy', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const result = await secureKeyManager.initializeWithPassword('validpassword123');
      
      // Verify that the key was derived with enhanced iterations
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalledWith(
        expect.objectContaining({
          iterations: 150001, // Enhanced iterations
        }),
        expect.anything(),
        expect.anything(),
        false,
        ['encrypt', 'decrypt']
      );
    });

    test('should use proper salt derivation', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      await secureKeyManager.initializeWithPassword('validpassword123');
      
      // Verify that salt is properly generated
      expect(mockCrypto.getRandomValues).toHaveBeenCalledWith(expect.any(Uint8Array));
    });
  });

  describe('IndexedDB Storage Security', () => {
    test('should initialize IndexedDB securely', async () => {
      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: null,
          onsuccess: null,
          onupgradeneeded: null,
          result: {
            objectStoreNames: {
              contains: jest.fn().mockReturnValue(false),
            },
            createObjectStore: jest.fn(),
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn().mockResolvedValue(undefined),
                get: jest.fn().mockResolvedValue(null),
                delete: jest.fn().mockResolvedValue(undefined),
              }),
            }),
          },
        };
        
        if (version === 1) {
          request.result.objectStoreNames.contains.mockReturnValue(true);
        }
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      await secureKeyManager.initializeWithPassword('validpassword123');
      
      expect(mockOpen).toHaveBeenCalledWith('CrowAISecureKeys', 1);
    });

    test('should store encrypted keys in IndexedDB', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
          encrypt: jest.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4, 5])),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: null,
          onsuccess: null,
          onupgradeneeded: null,
          result: {
            objectStoreNames: {
              contains: jest.fn().mockReturnValue(false),
            },
            createObjectStore: jest.fn(),
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn().mockResolvedValue(undefined),
                get: jest.fn().mockResolvedValue(null),
                delete: jest.fn().mockResolvedValue(undefined),
              }),
            }),
          },
        };
        
        if (version === 1) {
          request.result.objectStoreNames.contains.mockReturnValue(true);
        }
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      await secureKeyManager.initializeWithPassword('validpassword123');
      
      // Verify that encryption was called with proper parameters
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          iv: expect.any(Uint8Array),
        }),
        expect.anything(),
        expect.any(ArrayBuffer)
      );
    });

    test('should retrieve encrypted keys from IndexedDB', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
          encrypt: jest.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4, 5])),
          decrypt: jest.fn().mockResolvedValue(new Uint8Array([6, 7, 8, 9, 10])),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: null,
          onsuccess: null,
          onupgradeneeded: null,
          result: {
            objectStoreNames: {
              contains: jest.fn().mockReturnValue(false),
            },
            createObjectStore: jest.fn(),
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn().mockResolvedValue(undefined),
                get: jest.fn().mockResolvedValue({
                  keyId: 'test-key-id',
                  encryptedSecretKey: 'encrypted-data',
                  iv: 'iv-data',
                  timestamp: Date.now(),
                  version: '1.0'
                }),
                delete: jest.fn().mockResolvedValue(undefined),
              }),
            }),
          },
        };
        
        if (version === 1) {
          request.result.objectStoreNames.contains.mockReturnValue(true);
        }
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      const result = await secureKeyManager.initializeWithPassword('validpassword123');
      
      expect(result).toHaveProperty('publicKey');
      expect(result).toHaveProperty('keyId');
    });
  });

  describe('Key Rotation Mechanism', () => {
    test('should rotate encryption keys', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
          encrypt: jest.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4, 5])),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: null,
          onsuccess: null,
          onupgradeneeded: null,
          result: {
            objectStoreNames: {
              contains: jest.fn().mockReturnValue(false),
            },
            createObjectStore: jest.fn(),
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn().mockResolvedValue(undefined),
                get: jest.fn().mockResolvedValue(null),
                delete: jest.fn().mockResolvedValue(undefined),
              }),
            }),
          },
        };
        
        if (version === 1) {
          request.result.objectStoreNames.contains.mockReturnValue(true);
        }
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      const result = await secureKeyManager.rotateEncryptionKeys('validpassword123');
      
      expect(result).toHaveProperty('publicKey');
      expect(result).toHaveProperty('keyId');
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalledTimes(2); // Initial + rotation
    });

    test('should cleanup old keys during rotation', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
          encrypt: jest.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4, 5])),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: null,
          onsuccess: null,
          onupgradeneeded: null,
          result: {
            objectStoreNames: {
              contains: jest.fn().mockReturnValue(false),
            },
            createObjectStore: jest.fn(),
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn().mockResolvedValue(undefined),
                get: jest.fn().mockResolvedValue(null),
                delete: jest.fn().mockResolvedValue(undefined),
              }),
            }),
          },
        };
        
        if (version === 1) {
          request.result.objectStoreNames.contains.mockReturnValue(true);
        }
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      await secureKeyManager.rotateEncryptionKeys('validpassword123');
      
      // Verify that cleanup was attempted
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalled();
    });
  });

  describe('XSS Protection', () => {
    test('should validate security context before operations', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      // Test with secure context
      (global as any).window.isSecureContext = true;
      
      await secureKeyManager.initializeWithPassword('validpassword123');
      
      // Should work without issues
      expect(secureKeyManager.isSessionActive()).toBe(true);
    });

    test('should reject operations in insecure context', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      // Test with insecure context
      (global as any).window.isSecureContext = false;
      
      await expect(
        secureKeyManager.initializeWithPassword('validpassword123')
      ).rejects.toThrow('Security context validation failed');
    });
  });

  describe('Session Management', () => {
    test('should manage session state securely', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      await secureKeyManager.initializeWithPassword('validpassword123');
      
      expect(secureKeyManager.isSessionActive()).toBe(true);
      expect(secureKeyManager.getPublicKey()).not.toBeNull();
      expect(secureKeyManager.getSecretKey()).not.toBeNull();
      
      secureKeyManager.clearSession();
      
      expect(secureKeyManager.isSessionActive()).toBe(false);
      expect(secureKeyManager.getPublicKey()).toBeNull();
      expect(secureKeyManager.getSecretKey()).toBeNull();
    });

    test('should clear session securely', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      await secureKeyManager.initializeWithPassword('validpassword123');
      
      const publicKeyBefore = secureKeyManager.getPublicKey();
      const secretKeyBefore = secureKeyManager.getSecretKey();
      
      secureKeyManager.clearSession();
      
      expect(secureKeyManager.getPublicKey()).toBeNull();
      expect(secureKeyManager.getSecretKey()).toBeNull();
      expect(publicKeyBefore).not.toBeNull();
      expect(secretKeyBefore).not.toBeNull();
    });
  });

  describe('Storage Statistics', () => {
    test('should provide storage statistics', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'PBKDF2' },
            extractable: false,
            usages: ['deriveKey'],
            type: 'secret',
          }),
          deriveKey: jest.fn().mockResolvedValue({
            algorithm: { name: 'AES-GCM', length: 256 },
            extractable: false,
            usages: ['encrypt', 'decrypt'],
            type: 'secret',
          }),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: null,
          onsuccess: null,
          onupgradeneeded: null,
          result: {
            objectStoreNames: {
              contains: jest.fn().mockReturnValue(false),
            },
            createObjectStore: jest.fn(),
            transaction: jest.fn().mockReturnValue({
              objectStore: jest.fn().mockReturnValue({
                put: jest.fn().mockResolvedValue(undefined),
                get: jest.fn().mockResolvedValue(null),
                delete: jest.fn().mockResolvedValue(undefined),
              }),
            }),
          },
        };
        
        if (version === 1) {
          request.result.objectStoreNames.contains.mockReturnValue(true);
        }
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      const stats = await secureKeyManager.getStorageStats();
      
      expect(stats).toHaveProperty('hasKeys');
      expect(stats).toHaveProperty('keyCount');
      expect(stats).toHaveProperty('storageType');
      expect(typeof stats.hasKeys).toBe('boolean');
      expect(typeof stats.keyCount).toBe('number');
      expect(stats.storageType).toBe('indexeddb');
    });
  });

  describe('Error Handling', () => {
    test('should handle IndexedDB errors gracefully', async () => {
      const mockOpen = jest.fn().mockImplementation((name, version) => {
        const request = {
          onerror: jest.fn(),
          onsuccess: null,
          onupgradeneeded: null,
          error: new Error('Database error'),
        };
        
        return request;
      });

      (global as any).indexedDB.open = mockOpen;

      await expect(
        secureKeyManager.initializeWithPassword('validpassword123')
      ).rejects.toThrow('Failed to initialize secure encryption keys');
    });

    test('should handle crypto operation errors gracefully', async () => {
      const mockCrypto = {
        subtle: {
          importKey: jest.fn().mockRejectedValue(new Error('Crypto error')),
        },
        getRandomValues: jest.fn((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }),
      };

      (global as any).crypto = mockCrypto;

      await expect(
        secureKeyManager.initializeWithPassword('validpassword123')
      ).rejects.toThrow('Failed to initialize secure encryption keys');
    });
  });
});